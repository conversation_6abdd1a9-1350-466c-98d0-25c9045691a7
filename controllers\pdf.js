const puppeteer = require('puppeteer');
const handlebars = require('handlebars');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const { request, response } = require("express");
const { getPatientsByMiles, getPatientsGreaterFifteen, getAllTripsByDriverCenter, getNewTripsByCenter, getCancellations, getSpecialTranspByCenter, generateAllTrips, getKpiByCenter } = require('./reporting');
const { MedicalCenter } = require("../models/MedicalCenter");


const compileTemplate = (templateName, data) => {
  const filePath = path.join(__dirname, '../', 'pdf-templates', `${templateName}.handlebars`);
  const templateSource = fs.readFileSync(filePath, 'utf-8');
  const template = handlebars.compile(templateSource);
  return template(data);
};

const generatePatientByMilesIntervalPdf = async ( req = request, res = response )  => {
  try {
    const totalPatientByMiles = await getPatientsByMiles(req.body.dateIni, req.body.dateEnd, Number(req.body.medicalCenterId));

    const data = {
      reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
      totalPatients: totalPatientByMiles.totalPatients,
      medicalCenterName: totalPatientByMiles.medicalCenterName,
      dataByMiles: {
        '0to2Miles': totalPatientByMiles.dataArray[0],
        '2to5Miles': totalPatientByMiles.dataArray[1],
        '5to10Miles': totalPatientByMiles.dataArray[2],
        '10to15Miles': totalPatientByMiles.dataArray[3],
        'Greater15Miles': totalPatientByMiles.dataArray[4],
      }
    };

    const html = compileTemplate('patientByMilesInterval', data);

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true
    });
    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'domcontentloaded' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true
    });

    await browser.close();

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Length': pdfBuffer.length,
      'Content-Disposition': 'attachment; filename="patients_miles_interval_report.pdf"',
    });
    res.end(pdfBuffer);

  } catch (error) {
    console.error('Error generating patient by miles interval PDF: ', error);
    res.status(500).send('Error generating PDF');
  }
};

const generatePatientGreaterFifteenMilesPdf = async ( req = request, res = response )  => {
  try {
    const patientsByMiles = await getPatientsGreaterFifteen(req.body.dateIni, req.body.dateEnd, Number(req.body.medicalCenterId));

    let medicalCenterName = '';
    const medicalCenter = (await MedicalCenter.findAll()).filter(medicalCenter => medicalCenter.IdMedicalC === Number(req.body.medicalCenterId));
    if (medicalCenter.length != 0) {
       medicalCenterName = medicalCenter[0].Name;
    }

    const htmlHeader = `
    <!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patients By Miles Interval</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            padding: 20px 20px 20px 20px;
            text-align: center;
            box-sizing: border-box;
        }
        .header {
            font-size: 20px;
            font-weight: bold;
            margin: 10px 0;
        }
        .divider {
            background-color: #79a7e3;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        .info-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
         p {
            margin: 0;
            padding: 0;
            padding-top: 3px;
            font-size: medium;
            color: rgb(49, 71, 103);
        }
        .greater {
            padding: 5px 10px;
            border: 1px solid #000;
            border-radius: 2px;
            background-color: rgba(255, 255, 255, 255);
            font-weight: bold;
            color: #000;
        }
        .percentage {
            padding: 5px 10px;
            border: 1px solid #000;
            border-radius: 2px;
            background-color: rgba(255, 255, 255, 255);
            font-weight: bold;
            color: #000;
        }
        .date-time {
            padding: 5px 10px;
            margin-right: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(255, 255, 153, 255);
            color: #000;
        }
        .location {
            padding: 5px 10px;
            margin-left: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(230, 237, 215, 255);
            font-weight: bold;
            color: #000;
        }
        .table {
            width: 100%;
            max-width: 600px;
            padding-top: 30px;
            margin: 10px auto;
            border-collapse: separate;
            border-spacing: 5px;
        }
        .table td, .table th {
          word-wrap: break-word;
          overflow-wrap: break-word;
        }
        .table-data {
          page-break-inside: avoid;
        }
        .table th,
        .table td {
            border: none;
        }
        .table-header th {
            border: none;
            color: #79a7e3;
        }
        .table-data td {
            padding: 8px;
            text-align: center;
            border: none;
        }
        .table-data td:nth-child(1),
        .table-data td:nth-child(3) {
            border: 1px solid #ccc;
        }
        .table-data td:nth-child(1) {
            text-align: left;
            padding-left: 10px;
            color: #79a7e3;
        }
        .last-row td:nth-child(1) {
            border: none;
            font-weight: bold;
            text-align: right;
        }
        .last-row td:nth-child(2) {
            border: 1px solid #ccc;
            font-weight: bold;
        }
        .last-row td:nth-child(3) {
            border: none;
        }
        .spacer-row td {
            height: 20px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">SACS Report Patients By Miles Interval: Greater 15 Miles</div>
        <div class="divider">
            <span class="location">{{medicalCenterName}}</span>
            <div class="info-group">
              <p>Greater 15 Miles </p><span class="greater">{{greater15Miles}}</span>
            </div>
            <div class="info-group">
              <p>% Percentage </p><span class="percentage">{{percentage}}</span>
            </div>
            <span class="date-time">
                <p>{{reportTimeout}}</p>
            </span>
        </div>
        <table class="table">
            <tr class="table-header">
                <th>Patient Name</th>
                <th></th>
                <th>Distance</th>
            </tr>`;

    let htmlContent = '';

    patientsByMiles.dataArray.forEach(patient => {
      htmlContent += `
      <tr class="table-data">
				<td>${patient.name}</td>
        <td></td>
				<td>${patient.distance}</td>
				</tr>`
    });

    htmlContent = htmlContent.replace(/[\n\t\r]+/g, '').trim();

    const htmlFooter = `<tr class="spacer-row"></tr>
            <td colspan="3"></td>
            </tr>
            <tr class="table-data last-row">
                <td>Total</td>
                <td>{{totalPatients}}</td>
                <td></td>
            </tr>
        </table>
    </div>
</body>

      </html>`;

    const data = {
      reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
      totalPatients: patientsByMiles.totalPatients,
      medicalCenterName: medicalCenterName,
      percentage: patientsByMiles.percentage,
      greater15Miles: patientsByMiles.dataArray.length,
    };

    const template = handlebars.compile(htmlHeader+htmlContent+htmlFooter);
    const html = template(data);
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true
    });
    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'domcontentloaded' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true
    });

    await browser.close();

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Length': pdfBuffer.length,
      'Content-Disposition': 'attachment; filename="patients_greater_15_Miles_report.pdf"',
    });
    res.end(pdfBuffer);

  } catch (error) {
    console.error('Error generating patients greater fifteen miles PDF: ', error);
    res.status(500).send('Error generating PDF');
  }
};

const generateTripsByDriverCenterPdf = async ( req = request, res = response )  => {
  try {
    const trips = await getAllTripsByDriverCenter(req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest, Number(req.body.driverId));

    const processedDataArray = trips.dataArray.map(center => {
      const completedTrips = center.trips.filter(trip => trip.onBoard !== 'N/A' && trip.dropOff !== 'N/A');
      const notCompletedTrips = center.trips.filter(trip => trip.onBoard === 'N/A' || trip.dropOff === 'N/A');
      
      return {
        medicalCenterName: center.medicalCenterName,
        completedTrips: completedTrips,
        notCompletedTrips: notCompletedTrips,
        completedTripsCount: completedTrips.length,
        totalLateTrips: center.totalLateTrips
      };
    });

    const templatePath = path.resolve(__dirname, '../pdf-templates/trips_by_driver_template.html');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');

    const data = {
      reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
      reportQueryDate: trips.queryDate,
      driverName: trips.driverName,
      zone: trips.zone,
      vehicle: trips.vehicle,
      totalTrips: trips.totalTrips,
      completedTrips: trips.completedTrips,
      notCompletedTrips: trips.totalTrips - trips.completedTrips,
      effectiveWork: trips.effectiveWork,
      dataArray: processedDataArray
    };

    const template = handlebars.compile(templateHtml);
    const html = template(data);
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true
    });
    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'domcontentloaded' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      landscape: true,
    });

    await browser.close();

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Length': pdfBuffer.length,
      'Content-Disposition': 'attachment; filename="trips_by_driver_report.pdf"',
    });
    res.end(pdfBuffer);

  } catch (error) {
    console.error('Error generating trips by driver PDF: ', error);
    res.status(500).send('Error generating PDF');
  }
};

const generateNewTripsByCenterPdf = async ( req = request, res = response )  => {
  try {
    const trips = await getNewTripsByCenter(Number(req.body.idCenter), req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);

    const htmlHeader = `
    <!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Sheet By Driver</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            padding: 20px 20px 20px 20px;
            text-align: center;
            box-sizing: border-box;
        }
        .header {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }
        .divider {
            background-color: #79a7e3;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        .info-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        p {
            margin: 0;
            padding: 0;
            padding-top: 3px;
            color: rgb(49, 71, 103);
        }
        .header-value {
            padding: 5px 10px;
            border: 1px solid #000;
            border-radius: 2px;
            background-color: rgba(255, 255, 255, 255);
            font-weight: bold;
            color: #000;
        }
        .date-time {
            padding: 5px 10px;
            margin-right: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(255, 255, 153, 255);
            color: #000;
        }
        .location {
            padding: 5px 10px;
            margin-left: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(230, 237, 215, 255);
            font-weight: bold;
            color: #000;
        }
       .table {
          table-layout: fixed;
          width: 100%;
          padding-top: 30px;
          margin: 10px auto;
          border-collapse: collapse;
          border-spacing: 0;
        }
        .table td, .table th {
          word-wrap: break-word;
          overflow-wrap: break-word;
        }
        .table-data {
          page-break-inside: avoid;
        }
        .table th,
        .table-header th {
            border: none;
            color: #79a7e3;
        }
        .table-data td {
            padding: 6px;
            text-align: center;
            font-size: 10px;
            border-bottom: 1px solid #ccc;
        }
        .last-row td:nth-child(1) {
            border: none;
            font-weight: bold;
            text-align: right;
        }
        .last-row td:nth-child(2) {
            border: 1px solid #ccc;
            font-weight: bold;
        }
        .last-row td:nth-child(3) {
            border: none;
        }
        .spacer-row td {
            height: 20px;
        }
        .total-trips {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin-top: 10px;
        }
        .total-trips p {
          margin: 0;
          padding-right: 5px;
        }
        .query-date {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-top: 10px;
        }
        .work-header {
          text-align: center;
          font-weight: bold;
          color: #79a7e3;
          width: 30%;
        }
        th.performed-item  {
          font-weight: bold;
          font-size: 12px;
          color: #000000 !important;
          width: 7%;
        }
        .table-header th:nth-child(1) { width: 5%; }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">SACS Report New Trip</div>
        <div class="divider">
            <span class="location">{{medicalCenter}}</span>
            <div class="info-group">
              <p>Total Number Of Trips:</p><span class="header-value">{{totalTrips}}</span>
            </div>
            <span class="date-time">
                <p>{{reportTimeout}}</p>
            </span>
        </div>
        <br>
        <div style="display: flex; justify-content: space-between;">
            <div class="total-trips">
                <span></span>
            </div>
            <div class="query-date">
                <p><strong>Date:</strong>{{reportQueryDate}}</p>
            </div>
        </div>
        <br>`;

      let htmlContent = '<div style="margin-top: 30px;">';
      trips.dataArray.forEach(data => {

      htmlContent += `
            <div class="divider">
                <span class="location">${data.driverName}</span>
                <div class="info-group">
                  <p>Zone: </p><span class="header-value">${data.zone}</span>
                </div>
                <div class="info-group">
                  <p>Vehicle: </p><span class="header-value">${data.vehicle}</span>
                </div>
                <div class="info-group">
                  <p>Total Trips:</p><span class="header-value">${data.trips.length}</span>
                </div>
            </div>
          <br>
          <table class="table">
            <tr class="table-header">
              <th rowspan="2">Trip Id</th>
              <th rowspan="2">App Time</th>
              <th rowspan="2">Patient Name</th>
              <th rowspan="2">Pickup Location</th>
              <th rowspan="2">Special Needs</th>
              <th rowspan="2">Destination</th>
              <th colspan="4" class="work-header">Work Performed</th>
            </tr>
            <tr class="table-header">
              <th class="performed-item">On Board</th>
              <th class="performed-item">Drop Off</th>
              <th class="performed-item">Travel Time</th>
              <th class="performed-item">Late Arrival</th>
            </tr>`;

      data.trips.forEach(trip => {
        htmlContent += `
          <tr class="table-data">
            <td>${trip.tripId}</td>
            <td>${trip.appTime}<br><strong>Phones: </strong>${trip.patientPhones}</td>
            <td>${trip.patientName}</td>
            <td>${trip.pickupLocation}<br><strong>Reason: </strong>${trip.reason}</td>
            <td>${trip.specialNeeds}</td>
            <td>${trip.destination}</td>
            <td>${trip.onBoard}</td>
            <td>${trip.dropOff}</td>
            <td>${trip.travelTime}</td>
            <td>${trip.lateArrival}</td>
          </tr>`;
      });

      htmlContent += `
          </table>
          <div class="divider">
            <div class="info-group">
              <p>Total Trips: </p><span class="header-value">${data.trips.length}</span>
            </div>
            <div class="info-group">
              <p>Total Completed: </p><span class="header-value">${data.completedTrips}</span>
            </div>
            <div class="info-group">
              <p>Total Cancelled: </p><span class="header-value">${data.canceledTrips}</span>
            </div>
            <div class="info-group">
              <p>Total Late: </p><span class="header-value">${data.lateTrips}</span>
            </div>
            <div class="info-group">
              <p>Effective Work: </p><span class="header-value">${data.effectiveWork} %</span>
            </div>
          </div>
        <br>`;
    });

    htmlContent = htmlContent.replace(/[\n\t\r]+/g, '').trim();

    const htmlFooter = `
    </div></div></body></html>`;

    const data = {
      reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
      reportQueryDate: trips.queryDate,
      medicalCenter: trips.medicalCenter,
      totalTrips: trips.totalTrips,
    };

    const template = handlebars.compile(htmlHeader+htmlContent+htmlFooter);
    const html = template(data);
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true
    });
    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'domcontentloaded' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      landscape: true,
    });

    await browser.close();

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Length': pdfBuffer.length,
      'Content-Disposition': 'attachment; filename="new_trips_by_center_report.pdf"',
    });
    res.end(pdfBuffer);

  } catch (error) {
    console.error('Error generating new trips by center PDF: ', error);
    res.status(500).send('Error generating PDF');
  }
};

const generateCancellationsPdf = async (req = request, res = response) => {
    try {
      const cancellations = await getCancellations(
        Number(req.body.IdMedicalCenter),
        req.body.Driver,
        req.body.dateIni,
        req.body.dateEnd,
        req.body.isTodaysRequest,
        req.body.tripType
      );

      const path = require('path');
      const templatePath = path.resolve(__dirname, '../pdf-templates/cancellations_template.html');

      const fs = require('fs');
      const templateHtml = fs.readFileSync(templatePath, 'utf8');

      handlebars.registerHelper('ifNotEquals', function(arg1, arg2, options) {
        return (arg1 != arg2) ? options.fn(this) : options.inverse(this);
      });

      const data = {
        reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
        reportQueryDate: cancellations.queryDate,
        medicalCenter: cancellations.medicalCenter,
        totalTrips: cancellations.totalTrips,
        dataArray: cancellations.dataArray
      };

      const template = handlebars.compile(templateHtml);
      const html = template(data);

      const browser = await puppeteer.launch({
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        headless: true
      });

      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: 'networkidle0' });

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        landscape: true,
        margin: {
          top: '10mm',
          right: '10mm',
          bottom: '20mm',
          left: '10mm'
        },
        displayHeaderFooter: true,
        footerTemplate: `
          <div style="width: 100%; padding: 0 10mm; font-size: 9px; position: relative; margin-top: 10px; text-align: right; color: black;">
            <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
          </div>
        `,
        headerTemplate: '<span></span>'
      });

      await browser.close();

      res.set({
        'Content-Type': 'application/pdf',
        'Content-Length': pdfBuffer.length,
        'Content-Disposition': 'attachment; filename="cancellations_report.pdf"',
      });

      res.end(pdfBuffer);
    }
    catch (error) {
      console.error('Error generating cancellations PDF: ', error);
      res.status(500).send('Error generating PDF');
    }
  };

const generateSpecialTranspPdf = async ( req = request, res = response ) => {
  try {
    const trips = await getSpecialTranspByCenter(Number(req.body.medicalCenterId), Number(req.body.driverId), req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);

    const path = require('path');
    const templatePath = path.resolve(__dirname, '../pdf-templates/special_transp_template.html');

    const fs = require('fs');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');

    handlebars.registerHelper('ifNotEquals', function(arg1, arg2, options) {
      return (arg1 != arg2) ? options.fn(this) : options.inverse(this);
    });

    const data = {
      reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
      reportQueryDate: trips.queryDate,
      medicalCenter: trips.medicalCenter,
      totalTrips: trips.totalTrips,
      dataArray: trips.dataArray
    };

    const template = handlebars.compile(templateHtml);
    const html = template(data);

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true
    });

    const page = await browser.newPage();
    await page.setContent(html, { waitUntil: 'networkidle0' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      landscape: true,
      margin: {
        top: '10mm',
        right: '10mm',
        bottom: '20mm',
        left: '10mm'
      },
      displayHeaderFooter: true,
      footerTemplate: `
        <div style="width: 100%; padding: 0 10mm; font-size: 9px; position: relative; margin-top: 10px; text-align: right; color: black;">
          <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
        </div>
      `,
      headerTemplate: '<span></span>'
    });

    await browser.close();

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Length': pdfBuffer.length,
      'Content-Disposition': 'attachment; filename="special_transport_report.pdf"',
    });

    res.end(pdfBuffer);

  } catch (error) {
    console.error('Error generating special transport PDF: ', error);
    res.status(500).send('Error generating PDF');
  }
};

const generateAllTripsPdf = async (req = request, res = response) => {
  try {
    const allTripsData = await generateAllTrips(req.body.dateIni, req.body.dateEnd);

    const path = require('path');
    const templatePath = path.resolve(__dirname, '../pdf-templates/all_trips_template.html');

    const fs = require('fs');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');

    handlebars.registerHelper('ifNotEquals', function(arg1, arg2, options) {
      return (arg1 != arg2) ? options.fn(this) : options.inverse(this);
    });

    const data = {
        reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
        reportQueryDate: allTripsData.queryDate,
        totalTrips: allTripsData.totalTrips,
        totalCompleted: allTripsData.totalCompleted,
        totalCancelled: allTripsData.totalCancelled,
        pendingTrips: allTripsData.pendingTrips,
        completedPercentage: allTripsData.completedPercentage,
        cancelledPercentage: allTripsData.cancelledPercentage,
        pendingPercentage: allTripsData.pendingPercentage,
        groupedTrips: allTripsData.groupedTrips
      };

    const template = handlebars.compile(templateHtml);
    const html = template(data);

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true
    });

    const page = await browser.newPage();
    await page.setContent(html, { waitUntil: 'networkidle0' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      landscape: true,
      margin: {
        top: '10mm',
        right: '10mm',
        bottom: '20mm',
        left: '10mm'
      },
      displayHeaderFooter: true,
      footerTemplate: `
        <div style="width: 100%; padding: 0 10mm; font-size: 9px; position: relative; margin-top: 10px; text-align: right; color: black;">
          <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
        </div>
      `,
      headerTemplate: '<span></span>'
    });

    await browser.close();

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Length': pdfBuffer.length,
      'Content-Disposition': 'attachment; filename="all_trips_report.pdf"',
    });

    res.end(pdfBuffer);
  }
  catch (error) {
    console.error('Error generating all trips PDF: ', error);
    res.status(500).send('Error generating PDF');
  }
};

const generateKpiPdf = async (req = request, res = response) => {
  try {
    const kpiData = await getKpiByCenter(Number(req.body.medicalCenterId), req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);

    const path = require('path');
    const templatePath = path.resolve(__dirname, '../pdf-templates/kpi_template.html');

    const fs = require('fs');
    const templateHtml = fs.readFileSync(templatePath, 'utf8');

    handlebars.registerHelper('ifNotEquals', function(arg1, arg2, options) {
      return (arg1 != arg2) ? options.fn(this) : options.inverse(this);
    });

    const data = {
      reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
      reportQueryDate: kpiData.queryDate,
      reportTop: kpiData.reportTop,
      reportItems: kpiData.reportItems,
      reportReturnItems: kpiData.reportReturnItems,
      chartData: kpiData.chartData,
      chartBData: kpiData.chartBData
    };

    const template = handlebars.compile(templateHtml);
    const html = template(data);

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true
    });

   const page = await browser.newPage();
    await page.setContent(html, { waitUntil: 'networkidle0' });


    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      landscape: true,
      margin: {
        top: '10mm',
        right: '10mm',
        bottom: '20mm',
        left: '10mm'
      },
      displayHeaderFooter: true,
     footerTemplate: `
        <div style="width: 100%; padding: 0 10mm; font-size: 9px; position: relative; margin-top: 10px; text-align: right; color: black;">
          <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
        </div>
      `,
      headerTemplate: '<span></span>'
    });

    await browser.close();

    // Send the PDF as response
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Length': pdfBuffer.length,
      'Content-Disposition': 'attachment; filename="kpi_report.pdf"',
    });

    res.end(pdfBuffer);
  }
  catch (error) {
    console.error('Error generating KPI PDF: ', error);
    res.status(500).send('Error generating PDF');
  }
};

module.exports = {
  generatePatientByMilesIntervalPdf,
  generatePatientGreaterFifteenMilesPdf,
  generateTripsByDriverCenterPdf,
  generateNewTripsByCenterPdf,
  generateCancellationsPdf,
  generateSpecialTranspPdf,
  generateAllTripsPdf,
  generateKpiPdf
};