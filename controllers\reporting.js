
const { request, response } = require("express");
const { Sequelize,Op } = require("sequelize");
const { MedicalCenter } = require("../models/MedicalCenter");
const Patient = require("../models/Patient");
const { Trip } = require("../models/Trip");
const { Driver } = require('./../models/Driver');
const { Appointment } = require('./../models/Appointment');
const { DriverMC } = require('./../models/DriverMC');
const db2 = require('../db/connection');
const { QueryTypes } = require('sequelize');
const { format } = require("mysql2");
const moment = require('moment');
// OB: onboard. para maracar en app recogida,
// DO: dropoff. para maracar en app drop off,
// add clients number for by driver report
// button today by drivers report.

// medical centers
const getMedicalCenters = async ( req = request, res = response ) => {
  const medicalCenters = await MedicalCenter.findAll();
  return res.status(200).json({
    medicalCenters
  });
}

// drivers
/*const getDrivers = async (req = request, res = response) => {

  const drivers = await db2.query(
    'SELECT IdDrv Id,d.dZone dZone,m.IdMC IdMC,d.IdVehicle IdVehicle,d.Driver Driver,d.Phone1 Phone1, d.Address Address, d.pcolor pcolor  from driversmedicalc m INNER JOIN driver_assigments d ON (m.IdDrv=d.Id) INNER JOIN medical_centers mc ON (m.IdMC=mc.IdMedicalC) ORDER BY d.Driver;',
  {
    type: QueryTypes.SELECT
  });
  return res.json({
    drivers
  });
}*/

const getDrivers = async (req = request, res = response) => {
  const drivers = await Driver.findAll({
    where: {
        Enable: 1
      },
    order: ['Driver'],
  });
  return res.json({
    drivers
  });
}

// trips between dates
const getTripsBetweenDates = async (dateIni, dateEnd, type, isTodaysRequest, selectedMedicalCenterId, driverId, patientId) => {
  let trips = null;
  let table = Trip;

  switch (type) {
    case 'monitoring-status':
      trips = await table.findAll({
        where: {
          Date: {
            [Op.gte]: dateIni,
            [Op.lte]: dateEnd,
          },
          ...(patientId && { patNumber: patientId }),
          ...(driverId && { driver_id: driverId }),
          ...(selectedMedicalCenterId && { IdMC: selectedMedicalCenterId }),
        },
      });
    break;
    case 'by-new-trips-center':
      trips = await table.findAll({
        where: {
          Date: {
            [Op.gte]: dateIni,
            [Op.lte]: dateEnd,
          },
          TripType: 'A',
          notes: {
            [Op.like]: '%Added By Dispatcher%'
          }
        }
      });
    break;
    case 'by-special-transp-center':
      trips = await table.findAll({
        where: {
          Date: {
            [Op.gte]: dateIni,
            [Op.lte]: dateEnd,
          },
          TripType: 'A',
          special_requeriment: {
            [Op.or]: [
              { [Op.like]: '%Wheelchair%' },
              { [Op.like]: '%Walker%' },
              { [Op.like]: '%Companion%' }
            ]
          },
          ...(driverId && { driver_id: driverId }),
          ...(selectedMedicalCenterId && { IdMC: selectedMedicalCenterId }),
        }
      });
    break;
    case 'by-driver-center':
      trips = await table.findAll(
        {
          where: {
            Date: {
              [Op.gte]: dateIni,
              [Op.lte]: dateEnd,
            },
            TripType: 'A',
            ...(driverId && { driver_id: driverId }),
            CD:  null,
          }
        }
      );
    break;
    case 'by-driver':
      trips = await table.findAll(
        {
          where: {
            Date: {
              [Op.gte]: dateIni,
              [Op.lte]: dateEnd,
            },
            TripType: 'A'
          }
        }
      );
    break;
    case 'cancellations':
      trips = await table.findAll(
        {
          where: {
            /*Date: {
              [Op.gte]: dateIni,
              [Op.lte]: dateEnd,
            },*/
            Date: {
              [Op.between]: [dateIni,dateEnd]
            },
            CD: {
              [Op.not]: null
            },
            TripType: 'A'
          }
        }
      );
    break;
    case 'all-trips':
      trips = await table.findAll(
        {
          where: {
            Date: {
              [Op.gte]: dateIni,
              [Op.lte]: dateEnd,
            },
            // TripType: 'A' Verify change in all trips report
          }
        }
      );
    break;
    case 'kpi':
      trips = await table.findAll(
        {
          where: {
            Date: {
              [Op.gte]: dateIni,
              [Op.lte]: dateEnd,
            }
          }
        }
      );
    break;
    case 'patient-history':
        trips = await table.findAll({
          where: {
            Date: {
              [Op.gte]: dateIni,
              [Op.lte]: dateEnd,
            }
          }
        })
    break;
  }
  return trips;
}

// trips by driver by center
const getTripsByDriverCenter = async ( req = request, res = response ) => {
  try {
    let trips = await getTripsBetweenDates(
      req.body.dateIni, req.body.dateEnd, 'by-driver-center', req.body.isTodaysRequest, null, req.body.IdDriver, null);
    return res.status(200).json({
      trips
    });
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting trips info.'
    });
  }
}

// trips by driver
const getTripsByDriver = async ( req = request, res = response ) => {
  try {
    let trips = await getTripsBetweenDates(
      req.body.dateIni, req.body.dateEnd, 'by-driver', req.body.isTodaysRequest);
    return res.status(200).json({
      trips
    });
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting trips info.'
    });
  }
}

const getAllCancellations = async (req = request, res = response) => {
  try {
    const data = await getCancellations(
      Number(req.body.IdMedicalCenter),
      req.body.Driver,
      req.body.dateIni,
      req.body.dateEnd,
      req.body.isTodaysRequest,
      req.body.tripType
    );
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting cancellations information.'
    });
  }
}

const getCancellations = async (idCenter, idDriver, dateIni, dateEnd, isTodaysRequest, tripType ) => {
  try {
    let trips = null;
    const isDriver = idDriver !== null && idDriver !== undefined && idDriver !== 0;
    if(isDriver) {
      trips = await db2.query(
        'SELECT g.id,IdMC,Date,CD,OB,RP,TIME_FORMAT(TIME,"%H:%i:%s %p") TIME,FirstName,LastName,patient_id,patNumber,AddressPatient,PhoneNumber,MobilNumber,AddressDestination,ConsultDestination,Driver,TripType,driver_id,special_requeriment,outside_motive_details,comment,resourcename1,resourcename2,confirmstatus,c.CANCELLATION_TEXT notes FROM ges_appoinments g INNER JOIN cancellation_code c ON (g.Cod_Cancell=c.CANCELLATION_CODE) WHERE Date BETWEEN "'+dateIni+'" AND "'+dateEnd+'" AND IdMC='+idCenter+' AND CD IS NOT NULL AND ' + (tripType !== 'All' ? 'TripType = "'+tripType+'" AND ' : '') + 'confirmstatus = "Confirmed" AND resourcename2="TRANSPORT - YES" AND driver_id='+idDriver,
      {
        type: QueryTypes.SELECT
      });
    }
    else{
      trips = await db2.query(
        'SELECT g.id,IdMC,Date,CD,OB,RP,TIME_FORMAT(TIME,"%H:%i:%s %p") TIME,FirstName,LastName,patient_id,patNumber,AddressPatient,PhoneNumber,MobilNumber,AddressDestination,ConsultDestination,Driver,TripType,driver_id,special_requeriment,outside_motive_details,comment,resourcename1,resourcename2,confirmstatus,c.CANCELLATION_TEXT notes FROM ges_appoinments g INNER JOIN cancellation_code c ON (g.Cod_Cancell=c.CANCELLATION_CODE) WHERE Date BETWEEN "'+dateIni+'" AND "'+dateEnd+'" AND IdMC='+idCenter+' AND CD IS NOT NULL AND ' + (tripType !== 'All' ? 'TripType = "'+tripType+'" AND ' : '') + 'confirmstatus = "Confirmed" AND resourcename2="TRANSPORT - YES"',
      {
        type: QueryTypes.SELECT
      });
    }

      const medicalCenter = (await MedicalCenter.findAll()).filter(mc => mc.IdMedicalC === Number(idCenter))[0].Name;
      const drivers = isDriver ? (await Driver.findAll()).filter(d => d.Id === Number(idDriver)) : await Driver.findAll();

      const DateIni = new Date(dateIni);
        const PostDateIni = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateIni);

        const DateEnd = new Date(dateEnd);
        const PostDateEnd = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateEnd);

      const queryDate = isTodaysRequest ? PostDateIni :'From '+PostDateIni+' to '+PostDateEnd;

      const data = trips.map((trip)=> {

        const pickupDate = createSafeDate(trip.Date, trip.TIME);
        const pickupTime = pickupDate && !isNaN(pickupDate.getTime()) ?
        new Intl.DateTimeFormat('en-US', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: true
        }).format(pickupDate) : null;

        return {
          appTime: pickupTime,
          patientName: trip.FirstName + ' ' + trip.LastName,
          patientPhones: `${trip.PhoneNumber ?? '-'} / ${trip.MobilNumber ?? '-' }`,
          pickupLocation: trip.AddressPatient,
          specialNeeds: `${trip.special_requeriment ?? '-'}`,
          destination: trip.AddressDestination,
          comments: `${trip.comment ?? '-'}`,
          reason: `${trip.resourcename1 ?? '-'}`,
          medicalCenterId: trip.IdMC,
          driverId: trip.driver_id,
        };
      });

      const dataArray = data.reduce((acc, trip) => {
        const driver = drivers.find(d => d.Id === trip.driverId);
        const driverName = driver?.Driver || 'Unknown';

        let driverGroup = acc.find(group => group.driverName === driverName);
        if (!driverGroup) {
          driverGroup = {
            driverName,
            vehicle: driver?.IdVehicle || 'Unknown',
            zone: driver?.dZone || 'Unknown',
            trips: [],
          };
          acc.push(driverGroup);
        }
        driverGroup.trips.push(trip);

        return acc;
      }, []);

      let totalTrips = 0;
      dataArray.forEach(driverGroup => {
        totalTrips += driverGroup.trips.length;
      });

      dataArray.sort((a, b) => a.driverName.localeCompare(b.driverName));

      dataArray.forEach(driverGroup => {
        driverGroup.trips.forEach((trip, index) => {
          trip.tripId = index + 1;
        });
        driverGroup.trips.sort((a,b)=> a.patientName.localeCompare(b.patientName));
      });

      return {
        medicalCenter,
        totalTrips,
        queryDate,
        dataArray,
      };
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting cancellations info.'
    });
  }
}

function createSafeDate(dateStr, timeStr) {
  if (!dateStr || !timeStr) return null;

  try {
    const [year, month, day] = dateStr.split('-').map(Number);
    const [hours, minutes] = timeStr.split(':').map(Number);

    return new Date(year, month-1, day, hours, minutes);
  } catch (e) {
    console.error("Error creating date:", e);
    return null;
  }
}

// all trips
const getAllTrips = async (req = request, res = response) => {
  try {
    if(req.body.driverId !== null || req.body.patientId !== null || req.body.selectedMedicalCenterId !== null) {

      let trips = await getTripsBetweenDates(
        req.body.dateIni,
        req.body.dateEnd,
        'monitoring-status',
        req.body.isTodaysRequest,
        req.body.selectedMedicalCenterId,
        req.body.driverId,
        req.body.patientId,
        );

      return res.status(200).json({
        trips
      });
    }
    else {
      let trips = await getTripsBetweenDates(
        req.body.dateIni,
        req.body.dateEnd,
        'all-trips',
        req.body.isTodaysRequest
        );

      return res.status(200).json({
        trips
      });
    }

  } catch (error) {
    console.log(error);
    return res.status(500).json({
      msg: 'Error getting all trips.'
    });
  }
}

const getKpi = async (req = request, res = response) => {
  try {
    const data = await getKpiByCenter(Number(req.body.medicalCenterId), req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting KPI info.'
    });
  }
}

const getKpiByCenter = async (medicalCenterId, dateIni, dateEnd, isTodaysRequest ) => {
  try {
    let trips = await getTripsBetweenDates(
      dateIni,
      dateEnd,
      'kpi',
      isTodaysRequest);
    const found = trips.filter( item => item.IdMC === medicalCenterId );

      const DateIni = new Date(dateIni);
        const PostDateIni = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateIni);

        const DateEnd = new Date(dateEnd);
        const PostDateEnd = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateEnd);

      const queryDate = isTodaysRequest ? PostDateIni :'From '+PostDateIni+' to '+PostDateEnd;

    // Process data as done in frontend
    const completed = [];
    const realFound = found.filter((item) => item.resourcename2 === "TRANSPORT - YES");

    realFound.forEach((element) => {
      if (element.RP !== null && element.RAP !== null) {
        const newDate = new Date(element.RP);
        let item = {
          id: element.id,
          IdMC: element.IdMC,
          Date: element.Date,
          CD: element.CD,
          OB: element.OB,
          RAP: newDate,
          Time: element.Time,
          FirstName: element.FirstName,
          LastName: element.LastName,
          AddressPatient: element.AddressPatient,
          PhoneNumber: element.PhoneNumber,
          MobilNumber: element.MobilNumber,
          AddressDestination: element.AddressDestination,
          ConsultDestination: element.ConsultDestination,
          Driver: element.Driver,
          TripType: element.TripType,
          driver_id: element.driver_id,
          resourcename1: element.resourcename1,
          resourcename2: element.resourcename2,
          confirmstatus: element.confirmstatus
        };
        completed.push(item);
      }
    });

    // Get medical center info
    const medicalCenter = await MedicalCenter.findOne({
      where: { IdMedicalC: medicalCenterId }
    });

    // Set top values
    const reportTop = {
      title: 'KEY PERFORMANCE INDICATORS',
      medical_center: medicalCenter ? medicalCenter.Name : '',
      total_trips: 0,
      completed: {
        total: '0',
        percentage: '0'
      },
      timeout: new Date(),
      dates: {
        dateIni: new Date(dateIni),
        dateEnd: new Date(dateEnd)
      },
      onTime: {
        late: { total:'0', percentage: '0' },
        on_time: { total:'0', percentage: '0' }
      }
    };

    let totalTrips = found.length;
    reportTop.total_trips = totalTrips;
    reportTop.completed.total = completed.length.toString();
    reportTop.completed.percentage = totalTrips > 0 ?
      ((completed.length / totalTrips) * 100).toFixed(2) + "%" : "0%";

    // Set report items
    const reportItems = [];
    let lateTotal = 0;
    let aTripsTotal = 0;

    completed.forEach((compItem) => {
      const appointmentDate = new Date(compItem.Date + ' ' + compItem.Time);
      const obPickDate = new Date(compItem.OB);
      let onTime = 'Yes';

      if (appointmentDate < compItem.RAP) {
        onTime = 'No';
      }

      const duration = moment.duration(moment(compItem.RAP).diff(moment(obPickDate)));
      const minutes = Math.trunc(duration.asMinutes());
      const tripTime = calculateTravelTime(obPickDate, compItem.RAP);
      const mark = minutes > 45 ? '* ' : '';

      let item = {
        trip_type: compItem.TripType,
        patient_name: mark + compItem.FirstName + ' ' + compItem.LastName,
        appointment_date: moment(appointmentDate).format('HH:mm a, DD-MM-YY'),
        ob: moment(obPickDate).format('HH:mm a'),
        dp: moment(compItem.RAP).format('HH:mm a'),
        driver: compItem.Driver,
        on_time: onTime,
        trip_time: tripTime,
      };

      if (compItem.TripType === "A") {
        aTripsTotal += 1;
      }

      if ((appointmentDate < compItem.RAP) && (compItem.TripType === "A")) {
        lateTotal += 1;
      }

      reportItems.push(item);
    });

    // Calculate on-time percentages
    reportTop.onTime.late.total = lateTotal.toString();
    reportTop.onTime.late.percentage = aTripsTotal > 0 ?
      ((lateTotal / aTripsTotal) * 100).toFixed(2) : "0";

    reportTop.onTime.on_time.total = (aTripsTotal - lateTotal).toString();
    reportTop.onTime.on_time.percentage = aTripsTotal > 0 ?
      (((aTripsTotal - lateTotal) / aTripsTotal) * 100).toFixed(2) : "0";

    // Set return items
    const reportReturnItems = [];
    const returns = completed.filter(item => item.TripType === "B");

    returns.forEach(rItem => {
      let item = {
        patient_name: rItem.FirstName + ' ' + rItem.LastName,
        ob: moment(new Date(rItem.OB)).format('HH:mm a'),
        dp: moment(new Date(rItem.RAP)).format('HH:mm a'),
        driver: rItem.Driver
      };
      reportReturnItems.push(item);
    });

    // Set chart data
    const aType = found.filter(item => item.TripType === "A");
    const aOnBoard = aType.filter(item => item.OB !== null && item.RP === null).length;
    const aPending = aType.filter(item => item.OB === null && item.CD === null).length;
    const aCancelled = aType.filter(item => item.CD !== null).length;
    const aCompleted = aType.filter(item => item.RP !== null).length;

    const chartData = [
      ['Transit', aOnBoard, 0, 0, 0],
      ['Pending', 0, aPending, 0, 0],
      ['Completed', 0, 0, aCompleted, 0],
      ['Cancelled', 0, 0, 0, aCancelled]
    ];

    const bType = found.filter(item => item.TripType === "B");
    const bOnBoard = bType.filter(item => item.OB !== null && item.RP === null).length;
    const bPending = bType.filter(item => item.OB === null && item.CD === null).length;
    const bCancelled = bType.filter(item => item.CD !== null).length;
    const bCompleted = bType.filter(item => item.RP !== null).length;

    const chartBData = [
      ['Transit', bOnBoard, 0, 0, 0],
      ['Pending', 0, bPending, 0, 0],
      ['Completed', 0, 0, bCompleted, 0],
      ['Cancelled', 0, 0, 0, bCancelled]
    ];

     return {
        found,
        reportTop,
        reportItems,
        reportReturnItems,
        chartData,
        chartBData,
        queryDate,
    };

  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting KPI by center info.'
    });
  }
}

const getPatientHistory = async (req = request, res = response) => {
  const trips = await getTripsBetweenDates(
    req.body.strPostDateIni,
    req.body.strPostDateEnd,
    "patient-history"
  );

  const found = trips.filter(trip => {
    if (trip.patNumber === req.body.patientId) {
      return trip;
    }
  });

  res.status(200).json({
    ok: true,
    found
  })
}

const getAllPatients = async (req = request, res = response) => {
  try {
    let patients = await Patient.findAll();
    return res.status(200).json({ patients });

  } catch (error) {
    console.log(error);
    return res.status(500).json({
      error
    });
  }
}

// Patients by Miles Interval
const getPatientsByMilesInterval = async ( req = request, res = response ) => {
  try {
    const data = await getPatientsByMiles(
      req.body.dateIni, req.body.dateEnd, Number(req.body.medicalCenterId));
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting patients by miles interval info.'
    });
  }
}

const getPatientsByMiles = async (dateIni, dateEnd, medicalCenterId) => {
  try {

    const results = await Appointment.findAll({
      attributes: [
        'dist',
      ],
      where: {
        Date: {
          [Op.gte]: dateIni,
          [Op.lte]: dateEnd
        },
        TripType: 'A',
        IdMC: medicalCenterId,
      },
      raw: true
    });

    let medicalCenterName = '';
    const medicalCenter = (await MedicalCenter.findAll()).filter(medicalCenter => medicalCenter.IdMedicalC === medicalCenterId);
    if (medicalCenter.length != 0) {
       medicalCenterName = medicalCenter[0].Name;
    }

    let totalPatients = 0;
    let dataByMiles = {
      "0 to 2 Miles": { count: 0, percentage: 0 },
      "2 to 5 Miles": { count: 0, percentage: 0 },
      "5 to 10 Miles": { count: 0, percentage: 0 },
      "10 to 15 Miles": { count: 0, percentage: 0 },
      "Greater 15 Miles": { count: 0, percentage: 0 }
    };

    results.forEach(row => {
      const miles = (row.dist && parseFloat(row.dist.replace(' mi', ''))) || 0;

      if (miles > 0 && miles <= 2.9) {
        dataByMiles["0 to 2 Miles"].count ++;
      } else if (miles > 2.9 && miles <= 5.9) {
        dataByMiles["2 to 5 Miles"].count ++;
      } else if (miles > 5.9 && miles <= 10.9) {
        dataByMiles["5 to 10 Miles"].count ++;
      } else if (miles > 10.9 && miles <= 15.9) {
        dataByMiles["10 to 15 Miles"].count ++;
      } else if (miles > 15) {
        dataByMiles["Greater 15 Miles"].count ++;
      }
    });

    Object.values(dataByMiles).forEach(range => {
      totalPatients += range.count;
    });

    Object.keys(dataByMiles).forEach(range => {
      if (totalPatients > 0) {
        dataByMiles[range].percentage = ((dataByMiles[range].count / totalPatients) * 100).toFixed(2);
      }
    });

    const dataArray = Object.entries(dataByMiles).map(([interval, values]) => {
      return {
        interval,
        count: values.count,
        percentage: values.percentage
      };
    });

    return {
      medicalCenterName,
      totalPatients,
      dataArray
    };

  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting patients by miles interval info.'
    });
  }
};

// Patients by Miles Interval
const getPatientsByMilesIntervalNew = async ( req = request, res = response ) => {
  try {
    const data = await getPatientsByMilesNew(
      req.body.dateIni, req.body.dateEnd, Number(req.body.medicalCenterId), req.body.onlyMC);
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting patients by miles interval info.'
    });
  }
}

const getPatientsByMilesNew = async (dateIni, dateEnd, medicalCenterId, onlyMC) => {
  try {

    const results = await Appointment.findAll({
      attributes: [
        'dist',
        'PatNumber',
      ],
      where: {
        Date: {
          [Op.gte]: dateIni,
          [Op.lte]: dateEnd
        },
        TripType: 'A',
        IdMC: medicalCenterId,
      },
      raw: true
    });

    let medicalCenterName = '';
    let mcAddress = '';
    const medicalCenter = (await MedicalCenter.findAll()).filter(medicalCenter => medicalCenter.IdMedicalC === medicalCenterId);
    if (medicalCenter.length != 0) {
       medicalCenterName = medicalCenter[0].Name;
       mcAddress = medicalCenter[0].AddressMedicalC;
    }

    const patients = await db2.query(
      'SELECT G.Date AS appDate, G.Time AS appTime, G.AddressDestination AS destination, P.Names AS name, P.PatientAddress AS address, P.NumberPhone1 AS phone, G.dist AS distance, G.PatNumber FROM patients P INNER JOIN ges_appoinments G ON G.PatNumber = P.IdPatient WHERE G.TripType = "A" AND G.IdMC = '+medicalCenterId+' AND G.Date BETWEEN "'+dateIni+'" AND "'+dateEnd+'" GROUP BY PatNumber ORDER BY P.Names',
    {
      type: QueryTypes.SELECT
    });

    const patientMap = new Map();
    patients.forEach(patient => {
      patientMap.set(patient.PatNumber, patient);
    });

    let dataByMiles = {
      "0 to 2 Miles": { count: 0, percentage: 0, patients: [], counters: [], patientIndexMap: new Map() },
      "2 to 5 Miles": { count: 0, percentage: 0, patients: [], counters: [], patientIndexMap: new Map() },
      "5 to 10 Miles": { count: 0, percentage: 0, patients: [], counters: [], patientIndexMap: new Map() },
      "10 to 15 Miles": { count: 0, percentage: 0, patients: [], counters: [], patientIndexMap: new Map() },
      "Greater 15 Miles": { count: 0, percentage: 0, patients: [], counters: [], patientIndexMap: new Map() }
    };

    results.forEach(row => {
      const patient = patientMap.get(row.PatNumber);
      if (!patient) return;

      const miles = parseMiles(patient.distance);
      patient.distance = formatDistance(patient.distance);

      if (onlyMC && !patient.destination.includes(mcAddress)) return;

      let intervalKey = null;
      if (miles > 0 && miles <= 2.0) {
        intervalKey = "0 to 2 Miles";
      } else if (miles > 2.0 && miles <= 5.0) {
        intervalKey = "2 to 5 Miles";
      } else if (miles > 5.0 && miles <= 10.0) {
        intervalKey = "5 to 10 Miles";
      } else if (miles > 10.0 && miles <= 15.0) {
        intervalKey = "10 to 15 Miles";
      } else if (miles > 15.0) {
        intervalKey = "Greater 15 Miles";
      }

      if (intervalKey) {
        const intervalData = dataByMiles[intervalKey];
        const compositeKey = `${patient.name.trim().toLowerCase().replace(/,/g, '')}-${patient.distance}-${patient.phone}`;
        if (!intervalData.patientIndexMap.has(compositeKey)) {
          intervalData.patients.push(patient);
          intervalData.counters.push(1);
          intervalData.patientIndexMap.set(compositeKey, intervalData.patients.length - 1);
          intervalData.count++;
        } else {
          const index = intervalData.patientIndexMap.get(compositeKey);
          intervalData.counters[index]++;
        }
      }
    });

    let totalPatients = 0;
    Object.values(dataByMiles).forEach(range => {
      totalPatients += range.count;
    });

    Object.keys(dataByMiles).forEach(range => {
      const intervalData = dataByMiles[range];
      if (totalPatients > 0) {
        intervalData.percentage = ((intervalData.count / totalPatients) * 100).toFixed(2);
      }
      const combined = intervalData.patients.map((patient, index) => ({
        patient,
        counter: intervalData.counters[index]
      }));
      combined.sort((a, b) => a.patient.name.localeCompare(b.patient.name));
      intervalData.patients = combined.map(item => item.patient);
      intervalData.counters = combined.map(item => item.counter);
      delete intervalData.patientIndexMap;
    });

    const dataArray = Object.entries(dataByMiles).map(([interval, values]) => {
      return {
        interval,
        count: values.count,
        percentage: values.percentage,
        patients: values.patients,
        counters: values.counters
      };
    });

    return {
      medicalCenterName,
      totalPatients,
      dataArray
    };

  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting patients by miles interval info.'
    });
  }
};

const getPatientsGreaterFifteenMiles = async (req = request, res = response) => {
  try {
    const data = await getPatientsGreaterFifteen(
      req.body.dateIni, req.body.dateEnd, Number(req.body.medicalCenterId));
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting patients list greater fifteen miles info.'
    });
  }
}

const getPatientsGreaterFifteen = async (dateIni, dateEnd, medicalCenterId) => {
  try {
    const patients = await db2.query(
      'SELECT P.Names AS name, G.dist AS distance, G.PatNumber FROM patients P INNER JOIN ges_appoinments G ON G.PatNumber = P.IdPatient WHERE G.distance_range > 3 AND G.TripType = "A" AND G.IdMC = '+medicalCenterId+' AND G.Date BETWEEN "'+dateIni+'" AND "'+dateEnd+'" GROUP BY PatNumber ORDER BY P.Names',
    {
      type: QueryTypes.SELECT
    });
    const totalPatients = (await Patient.findAll()).length;
    const percentage =  totalPatients > 0 ? ((patients.length / totalPatients) * 100).toFixed(2) : 0;

    const dataArray = patients.map(patient => {
      return {
        name: patient.name,
        distance: parseFloat(patient.distance.replace(' mi', ''))
      };
    });

    return {
      totalPatients,
      percentage,
      dataArray
    };

  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting patients greater fifteen miles info.'
    });
  }
};

const getAllTripsByDriverCenter = async (dateIni, dateEnd, isTodaysRequest, idDriver ) => {
  try {
    let trips = await getTripsBetweenDates(
      dateIni, dateEnd, 'by-driver-center', isTodaysRequest, null, idDriver, null);

    if (!trips || trips.length === 0) {
      return {
        driverName: 'Unknown',
        zone: 0,
        vehicle: 0,
        totalTrips: 0,
        completedTrips: 0,
        dataArray: [],
        queryDate: '',
        effectiveWork: 0
      };
    }

    const allMedicalCenters = await MedicalCenter.findAll();

      let driverName = '';
      let zone = 0;
      let vehicle = 0;

      const driver = (await Driver.findAll()).filter(driver => driver.Id === trips[0].driver_id);
      if (driver.length != 0) {
         driverName = driver[0].Driver;
         zone = driver[0].dZone;
         vehicle = driver[0].IdVehicle;
      }

      const DateIni = new Date(dateIni);
        const PostDateIni = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateIni);

        const DateEnd = new Date(dateEnd);
        const PostDateEnd = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateEnd);

      const queryDate = isTodaysRequest ? PostDateIni :'From '+PostDateIni+' to '+PostDateEnd;

      const totalTrips = trips.length;
      const canceledTrips = trips.filter(trip => trip.CD !== null).length;
      const completedTrips = trips.filter(trip => trip.OB !== null && trip.RP !== null).length;
      const total = totalTrips - canceledTrips;
      const effectiveWork = total > 0 ? ((completedTrips / total) * 100).toFixed(2) : 0;

      const data = trips.map((trip)=> {

        const pickupDate = new Date(`${trip.Date} ${trip.Time}`);
        const pickupTime = new Intl.DateTimeFormat('en-US', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: true
        }).format(pickupDate);

        return {
          appTime: pickupTime,
          patientName: trip.FirstName + ' ' + trip.LastName,
          patientPhones: `${trip.PhoneNumber ?? '-'} / ${trip.MobilNumber ?? '-' }`,
          pickupLocation: trip.AddressPatient,
          specialNeeds: `${trip.special_requeriment ?? '-'}`,
          destination: trip.AddressDestination,
          comments: trip.comment,
          reason: `${trip.resourcename1 ?? '-'}`,
          medicalCenterId: trip.IdMC,
          onBoard: (trip.OB && trip.OB !== "") ? moment(trip.OB).format('h:mm A') : 'N/A',
          dropOff: (trip.RP && trip.RP !== "") ? moment(trip.RP).format('h:mm A') : 'N/A',
          travelTime: (trip.OB && trip.OB !== "" && trip.RP && trip.RP  !== "") ? calculateTravelTime(trip.OB, trip.RP) : 'N/A',
          lateArrival: trip.RP && trip.RP  !== "" ? (new Date(trip.RP) > pickupDate) ? 'Yes' : 'No' : 'N/A',
          cancelled: trip.CD && trip.CD !== "" ? true : false,
        };
      });

      data.sort((a, b) => {
        const dateA = new Date(`1970-01-01 ${a.appTime}`);
        const dateB = new Date(`1970-01-01 ${b.appTime}`);
        return dateA - dateB;
      });

     const dataArray = data.reduce((acc, trip) => {
        const medicalCenterName = allMedicalCenters.find(center => center.IdMedicalC === trip.medicalCenterId)?.Name || 'Unknown';

        let medicalCenterGroup = acc.find(group => group.medicalCenterName === medicalCenterName);
        if (!medicalCenterGroup) {
          medicalCenterGroup = {
            medicalCenterName,
            trips: [],
            totalLateTrips: 0,
          };
          acc.push(medicalCenterGroup);
        }
        medicalCenterGroup.trips.push(trip);
        if (trip.lateArrival === 'Yes') {
          medicalCenterGroup.totalLateTrips += 1;
        }
        return acc;
      }, []);

      dataArray.forEach(centerGroup => {
        centerGroup.trips.forEach((trip, index) => {
          trip.tripId = index + 1;
        });
      });

      return {
        driverName,
        zone,
        vehicle,
        totalTrips,
        completedTrips,
        dataArray,
        queryDate,
        effectiveWork,
      };
  } catch (error) {
    console.error('Error in getAllTripsByDriverCenter:', error);
    return {
      error: true,
      driverName: 'Unknown',
      zone: 0,
      vehicle: 0,
      totalTrips: 0,
      completedTrips: 0,
      dataArray: [],
      queryDate: '',
      effectiveWork: 0,
      errorMessage: 'Error getting all trips by driver center info.'
    };
  }
}

const getSpecialTransp = async (req = request, res = response) => {
  try {
    const data = await getSpecialTranspByCenter(Number(req.body.medicalCenterId), Number(req.body.driverId),
      req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting special transport info.'
    });
  }
}

const getSpecialTranspByCenter = async (idCenter, idDriver, dateIni, dateEnd, isTodaysRequest ) => {
   try {
    let trips = await getTripsBetweenDates(
      dateIni, dateEnd, 'by-special-transp-center', isTodaysRequest, idCenter, idDriver );

      const medicalCenter = (await MedicalCenter.findAll()).filter(mc => mc.IdMedicalC === Number(idCenter))[0].Name;
      const drivers = idDriver ? (await Driver.findAll()).filter(driver => driver.Id === idDriver) : await Driver.findAll();

      const DateIni = new Date(dateIni);
        const PostDateIni = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateIni);

        const DateEnd = new Date(dateEnd);
        const PostDateEnd = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateEnd);

      const queryDate = isTodaysRequest ? PostDateIni :'From '+PostDateIni+' to '+PostDateEnd;

      const data = trips.map((trip)=> {
        const pickupDate = (!trip.Date || !trip.Time) ? new Date(`${trip.Date} ${trip.Time}`) : null;
        const pickupTime = new Intl.DateTimeFormat('en-US', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: true
        }).format(pickupDate);

        return {
          appTime: pickupTime,
          patientName: trip.FirstName + ' ' + trip.LastName,
          patientPhones: `${trip.PhoneNumber ?? '-'} / ${trip.MobilNumber ?? '-' }`,
          pickupLocation: trip.AddressPatient,
          specialNeeds: `${trip.special_requeriment ? trip.special_requeriment.trim().replace(/,\s*$/, '') : '-'}`,
          destination: trip.AddressDestination,
          comments: trip.comment,
          reason: `${trip.resourcename1 ?? '-'}`,
          medicalCenterId: trip.IdMC,
          driverId: trip.driver_id,
          onBoard: (trip.OB && trip.OB !== "") ? moment(trip.OB).format('h:mm A') : 'N/A',
          dropOff: (trip.RP && trip.RP !== "") ? moment(trip.RP).format('h:mm A') : 'N/A',
          travelTime: (trip.OB && trip.OB !== "" && trip.RP && trip.RP !== "") ? calculateTravelTime(trip.OB, trip.RP) : 'N/A',
          lateArrival: trip.RP && trip.RP !== "" && pickupDate ? (new Date(trip.RP) > pickupDate) ? 'Yes' : 'No' : 'N/A',
          cancelled: trip.CD && trip.CD !== "" ? true : false,
        };
      });

      const dataArray = data.reduce((acc, trip) => {
        const driver = drivers.find(d => d.Id === trip.driverId);
        const driverName = driver?.Driver || 'Unknown';

        let driverGroup = acc.find(group => group.driverName === driverName);
        if (!driverGroup) {
          driverGroup = {
            driverName,
            vehicle: driver?.IdVehicle || 'Unknown',
            zone: driver?.dZone || 'Unknown',
            trips: [],
            canceledTrips: 0,
            lateTrips: 0,
            completedTrips: 0,
            effectiveWork: 0,
            totalTrips: 0,
          };
          acc.push(driverGroup);
        }
        driverGroup.trips.push(trip);
        if( trip.cancelled === true) {
          driverGroup.canceledTrips += 1;
        }
        if (trip.lateArrival === 'Yes') {
          driverGroup.lateTrips += 1;
        }
        if(trip.onBoard !== 'N/A' && trip.dropOff !== 'N/A') {
          driverGroup.completedTrips += 1;
        }
        driverGroup.totalTrips += 1;
        return acc;
      }, []);

      let totalTrips = 0;
      dataArray.forEach(driverGroup => {
        totalTrips += driverGroup.trips.length;
        const nonCancelledTrips = driverGroup.trips.length - driverGroup.canceledTrips;

        driverGroup.effectiveWork = nonCancelledTrips > 0
          ? ((driverGroup.completedTrips / driverGroup.trips.length) * 100).toFixed(2)
          : 0;
      });

      dataArray.sort((a, b) => a.driverName.localeCompare(b.driverName));

      dataArray.forEach(driverGroup => {
        driverGroup.trips.forEach((trip, index) => {
          trip.tripId = index + 1;
        });
      });

      return {
        medicalCenter,
        totalTrips,
        queryDate,
        dataArray,
      };
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting special transport by center info.'
    });
  }
}

const getNewTrips = async (req = request, res = response) => {
  try {
    const data = await getNewTripsByCenter(Number(req.body.idCenter),
      req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting new trips info.'
    });
  }
}

const getNewTripsByCenter = async (idCenter, dateIni, dateEnd, isTodaysRequest ) => {
  try {
    let trips = await getTripsBetweenDates(
      dateIni, dateEnd, 'by-new-trips-center', isTodaysRequest, idCenter);

      const medicalCenter = (await MedicalCenter.findAll()).filter(mc => mc.IdMedicalC === Number(idCenter))[0].Name;
      const drivers = await Driver.findAll();

      const DateIni = new Date(dateIni);
        const PostDateIni = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateIni);

        const DateEnd = new Date(dateEnd);
        const PostDateEnd = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateEnd);

      const queryDate = isTodaysRequest ? PostDateIni :'From '+PostDateIni+' to '+PostDateEnd;

      const data = trips.map((trip)=> {

        const pickupDate = (!trip.Date || !trip.Time) ? new Date(`${trip.Date} ${trip.Time}`) : null;
        const pickupTime = new Intl.DateTimeFormat('en-US', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: true
        }).format(pickupDate);

        return {
          appTime: pickupTime,
          patientName: trip.FirstName + ' ' + trip.LastName,
          patientPhones: `${trip.PhoneNumber ?? '-'} / ${trip.MobilNumber ?? '-' }`,
          pickupLocation: trip.AddressPatient,
          specialNeeds: `${trip.special_requeriment ? trip.special_requeriment.trim().replace(/,\s*$/, '') : '-'}`,
          destination: trip.AddressDestination,
          comments: trip.comment,
          reason: `${trip.resourcename1 ?? '-'}`,
          medicalCenterId: trip.IdMC,
          driverId: trip.driver_id,
          onBoard: (trip.OB && trip.OB !== "") ? moment(trip.OB).format('h:mm A') : 'N/A',
          dropOff: (trip.RP && trip.RP !== "") ? moment(trip.RP).format('h:mm A') : 'N/A',
          travelTime: (trip.OB && trip.OB !== "" && trip.RP && trip.RP !== "") ? calculateTravelTime(trip.OB, trip.RP) : 'N/A',
          lateArrival: trip.RP && trip.RP !== "" && pickupDate ? (new Date(trip.RP) > pickupDate) ? 'Yes' : 'No' : 'N/A',
          cancelled: trip.CD && trip.CD !== "" ? true : false,
        };
      });

      const dataArray = data.reduce((acc, trip) => {
        const driver = drivers.find(d => d.Id === trip.driverId);
        const driverName = driver?.Driver || 'Unknown';

        let driverGroup = acc.find(group => group.driverName === driverName);
        if (!driverGroup) {
          driverGroup = {
            driverName,
            vehicle: driver?.IdVehicle || 'Unknown',
            zone: driver?.dZone || 'Unknown',
            trips: [],
            canceledTrips: 0,
            lateTrips: 0,
            completedTrips: 0,
            effectiveWork: 0,
            totalTrips: 0,
          };
          acc.push(driverGroup);
        }
        driverGroup.trips.push(trip);
        if( trip.cancelled === true) {
          driverGroup.canceledTrips += 1;
        }
        if (trip.lateArrival === 'Yes') {
          driverGroup.lateTrips += 1;
        }
        if(trip.onBoard !== 'N/A' && trip.dropOff !== 'N/A') {
          driverGroup.completedTrips += 1;
        }
        driverGroup.totalTrips += 1;
        return acc;
      }, []);

      let totalTrips = 0;
      dataArray.forEach(driverGroup => {
        totalTrips += driverGroup.trips.length;
        const nonCancelledTrips = driverGroup.trips.length - driverGroup.canceledTrips;

        driverGroup.effectiveWork = nonCancelledTrips > 0
          ? ((driverGroup.completedTrips / driverGroup.trips.length) * 100).toFixed(2)
          : 0;
      });

      dataArray.sort((a, b) => a.driverName.localeCompare(b.driverName));

      dataArray.forEach(driverGroup => {
        driverGroup.trips.forEach((trip, index) => {
          trip.tripId = index + 1;
        });
      });

      return {
        medicalCenter,
        totalTrips,
        queryDate,
        dataArray,
      };
  } catch (error) {
    return res.status(500).json({
      msg: 'Error getting new trips by center info.'
    });
  }
}

function calculateTravelTime(onBoard, dropOff) {
  const onBoardDate = new Date(onBoard);
  const dropOffDate = new Date(dropOff);
  let travel_time = '';
  const differenceInMs = dropOffDate.getTime() - onBoardDate.getTime();

  const differenceInMinutes = Math.floor(differenceInMs / (1000 * 60));
  const differenceInHours = Math.floor(differenceInMinutes / 60);
  const remainingMinutes = differenceInMinutes % 60;

  if (differenceInHours >= 1) {
    travel_time = `${differenceInHours} hr${differenceInHours > 1 ? 's' : ''} ${remainingMinutes > 0 ? remainingMinutes + ' Min' : ''}`;
  } else {
    travel_time = `${differenceInMinutes} Min`;
  }

  return travel_time;
}

// Utils
const parseMiles = (distanceStr) => {
  if (!distanceStr) return 0;
  const numericValue = parseFloat(distanceStr.replace(' mi', ''));
  return isNaN(numericValue) ? 0 : numericValue;
};

const formatDistance = (distanceStr) => {
  const numericValue = parseMiles(distanceStr);
  return numericValue.toFixed(1) + ' mi';
};

const getDailyTripPerformance = async (req = request, res = response) => {
  try {
    const data = await generateDailyTripPerformance(
      req.body.dateIni, req.body.dateEnd, Number(req.body.medicalCenterId));

    if (data.error) {
      return res.status(500).json({
        msg: data.msg || 'Error generating daily performance.'
      });
    }

    return res.status(200).json(data);
  } catch (error) {
    console.error('Error in getDailyTripPerformance:', error);
    return res.status(500).json({
      msg: 'Error generating daily performance.'
    });
  }
}

const generateDailyTripPerformance = async (dateIni, dateEnd, medicalCenterId) => {
  try {

    const tripData = await db2.query(
      'SELECT DATE(g.Date) AS date, COUNT(g.id) AS totalTrips, SUM(CASE WHEN g.Cod_Cancell IS NOT NULL THEN 1 ELSE 0 END) AS canceledTrips, SUM(CASE WHEN g.Cod_Cancell IS NULL THEN 1 ELSE 0 END) AS completedTrips, ROUND(SUM(CASE WHEN g.distance IS NOT NULL THEN g.distance ELSE 0 END), 2) AS totalMiles, ROUND(AVG(CASE WHEN g.distance IS NOT NULL THEN g.distance ELSE 0 END), 2) AS avgMilesPerTrip, ROUND(AVG(CASE WHEN g.OB IS NOT NULL AND g.RP IS NOT NULL THEN TIMESTAMPDIFF(MINUTE, g.OB, g.RP) ELSE NULL END), 2) AS avgTripDuration, COUNT(DISTINCT g.PatNumber) AS totalPatients, COUNT(DISTINCT g.driver_id) AS totalDrivers, COUNT(DISTINCT g.IdMC) AS totalMedicalCenters FROM ges_appoinments g WHERE Date BETWEEN "'+dateIni+'" AND "'+dateEnd+'" AND g.IdMC='+medicalCenterId+' AND g.TripType = "A" GROUP BY DATE(g.Date) ORDER BY DATE(g.Date) ASC',
      {
        type: QueryTypes.SELECT
      });

    const formattedData = tripData.map(day => {
      const cancellationRate = day.totalTrips > 0
        ? parseFloat(((day.canceledTrips / day.totalTrips) * 100).toFixed(2))
        : 0;

      const completionRate = day.totalTrips > 0
        ? parseFloat(((day.completedTrips / day.totalTrips) * 100).toFixed(2))
        : 0;

      const formattedDate = new Date(day.date).toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      return {
        date: day.date,
        formattedDate,
        stats: {
          totalTrips: day.totalTrips,
          completedTrips: day.completedTrips,
          canceledTrips: day.canceledTrips,
          cancellationRate: cancellationRate,
          completionRate: completionRate,
          totalMiles: day.totalMiles,
          avgMilesPerTrip: day.avgMilesPerTrip,
          avgTripDuration: day.avgTripDuration,
          totalPatients: day.totalPatients,
          totalDrivers: day.totalDrivers,
          totalMedicalCenters: day.totalMedicalCenters
        }
      };
    });

    const totals = {
      totalDays: formattedData.length,
      totalTrips: 0,
      completedTrips: 0,
      canceledTrips: 0,
      totalMiles: 0
    };

    tripData.forEach(day => {
      totals.totalTrips += parseInt(day.totalTrips || 0);
      totals.completedTrips += parseInt(day.completedTrips || 0);
      totals.canceledTrips += parseInt(day.canceledTrips || 0);
      totals.totalMiles += parseFloat(day.totalMiles || 0);
    });

    totals.totalMiles = parseFloat(totals.totalMiles.toFixed(2));

    totals.avgTripsPerDay = formattedData.length > 0 ?
      parseFloat((totals.totalTrips / formattedData.length).toFixed(2)) : 0;
    totals.avgMilesPerDay = formattedData.length > 0 ?
      parseFloat((totals.totalMiles / formattedData.length).toFixed(2)) : 0;

    totals.cancellationRate = totals.totalTrips > 0
      ? parseFloat(((totals.canceledTrips / totals.totalTrips) * 100).toFixed(2))
      : 0;

    totals.completionRate = totals.totalTrips > 0
      ? parseFloat(((totals.completedTrips / totals.totalTrips) * 100).toFixed(2))
      : 0;

    const drivers = await Driver.findAll({
      where: {
        IdMC: medicalCenterId,
        Enable: 1
      },
      order: ['Driver']
    });

    const driversPerformance = [];

    for (const driver of drivers) {
      try {
        const driverData = await getAllTripsByDriverCenter(
          dateIni,
          dateEnd,
          false, // isTodaysRequest
          driver.Id
        );

        driversPerformance.push({
          driverId: driver.Id,
          driverName: driver.Driver,
          driverZone: driver.dZone,
          driverVehicle: driver.IdVehicle,
          performance: driverData
        });
      } catch (error) {
        console.error(`Error getting data for driver ${driver.Driver}:`, error);
      }
    }

    return {
      dailyData: formattedData,
      summary: totals,
      dateRange: {
        start: dateIni,
        end: dateEnd
      },
      driversPerformance: driversPerformance
    };

  } catch (error) {
    console.error('Error in generateDailyTripPerformance:', error);
    return {
      error: true,
      msg: 'Error generating daily performance report'
    };
  }
};

const generateAllTrips = async (dateIni, dateEnd) => {
  try {
    const trips = await Trip.findAll({
      where: {
        Date: {
          [Op.gte]: dateIni,
          [Op.lte]: dateEnd,
        },
      },
      order: [['IdMC', 'ASC'], ['Driver', 'ASC']],
    });

    const medicalCenters = await MedicalCenter.findAll();
    const drivers = await Driver.findAll();

    const groupedTrips = {};
    let totalCompleted = 0;
    let totalCancelled = 0;
    const totalTrips = trips.length;

    trips.forEach(trip => {
      if (trip.OB !== null && trip.RP !== null) {
        totalCompleted++;
      }
      if (trip.CD !== null) {
        totalCancelled++;
      }

      const center = medicalCenters.find(mc => mc.IdMedicalC === trip.IdMC)
      const centerName = center !== undefined && center !== '' ? center.Name : 'Sin definir';

      let driverName = 'Sin definir';
      if(trip.Driver !== undefined && trip.Driver !== '')
      {
        const driver = drivers.find(dr => dr.Id === trip.driver_id);
        driverName = driver !== undefined ? driver.Driver : driverName;
      }

      if (!groupedTrips[centerName]) {
        groupedTrips[centerName] = { drivers: {}, totalTrips: 0, completedTrips: 0, cancelledTrips: 0 };
      }

      if (!groupedTrips[centerName].drivers[driverName]) {
        groupedTrips[centerName].drivers[driverName] = [];
      }

      groupedTrips[centerName].drivers[driverName].push(trip);
      groupedTrips[centerName].totalTrips++;
    });

    for (const medicalCenter in groupedTrips) {
      let centerCompleted = 0;
      let centerCancelled = 0;

      for (const driver in groupedTrips[medicalCenter].drivers) {
        const trips = groupedTrips[medicalCenter].drivers[driver];
        let driverCompleted = 0;
        let driverCancelled = 0;

        trips.forEach(trip => {
          if (trip.OB !== null && trip.RP !== null) {
            driverCompleted++;
          }
          if (trip.CD !== null) {
            driverCancelled++;
          }
        });

        const driverPending = trips.length - (driverCompleted + driverCancelled);
        groupedTrips[medicalCenter].drivers[driver].stats = {
          Scheduled: trips.length,
          Completed: driverCompleted,
          Cancelled: driverCancelled,
          Pending: driverPending,
          CompletedPercentage: trips.length > 0 ? ((driverCompleted / trips.length) * 100).toFixed(2) : 0,
          CancelledPercentage: trips.length > 0 ? ((driverCancelled / trips.length) * 100).toFixed(2) : 0,
          PendingPercentage: trips.length > 0 ? ((driverPending / trips.length) * 100).toFixed(2) : 0,
        };

        centerCompleted += driverCompleted;
        centerCancelled += driverCancelled;
      }

      const centerPending = groupedTrips[medicalCenter].totalTrips - (centerCompleted + centerCancelled);
      groupedTrips[medicalCenter].completedTrips = centerCompleted;
      groupedTrips[medicalCenter].cancelledTrips = centerCancelled;
      groupedTrips[medicalCenter].pendingTrips = centerPending;
      groupedTrips[medicalCenter].completedPercentage = groupedTrips[medicalCenter].totalTrips > 0 ? ((centerCompleted / groupedTrips[medicalCenter].totalTrips) * 100).toFixed(2) : 0;
      groupedTrips[medicalCenter].cancelledPercentage = groupedTrips[medicalCenter].totalTrips > 0 ? ((centerCancelled / groupedTrips[medicalCenter].totalTrips) * 100).toFixed(2) : 0;
      groupedTrips[medicalCenter].pendingPercentage = groupedTrips[medicalCenter].totalTrips > 0 ? ((centerPending / groupedTrips[medicalCenter].totalTrips) * 100).toFixed(2) : 0;
    }

     const DateIni = new Date(dateIni);
        const PostDateIni = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateIni);

        const DateEnd = new Date(dateEnd);
        const PostDateEnd = new Intl.DateTimeFormat('en-US', {
          month: 'long',
          day: '2-digit',
          year: 'numeric',
        }).format(DateEnd);

    const queryDate = 'From '+PostDateIni+' to '+PostDateEnd;

    const pendingTrips = totalTrips - (totalCompleted + totalCancelled);
    const pendingPercentage = ((pendingTrips / totalTrips) * 100).toFixed(2);
    const completedPercentage = ((totalCompleted / totalTrips) * 100).toFixed(2);
    const cancelledPercentage = ((totalCancelled / totalTrips) * 100).toFixed(2);

    return {
      queryDate,
      totalTrips,
      totalCompleted,
      totalCancelled,
      pendingTrips,
      completedPercentage,
      cancelledPercentage,
      pendingPercentage,
      groupedTrips
    };

  } catch (error) {
    console.error('Error in generateAllTrips: ', error);
    throw error;
  }
};

module.exports = {
  getDailyTripPerformance,
  generateDailyTripPerformance,
  getMedicalCenters,
  getDrivers,
  getTripsByDriver,
  getTripsByDriverCenter,
  getTripsBetweenDates,
  getAllCancellations,
  getCancellations,
  getAllTrips,
  getKpi,
  getPatientHistory,
  getAllPatients,
  getPatientsByMilesInterval,
  getPatientsByMiles,
  getPatientsByMilesIntervalNew,
  getPatientsByMilesNew,
  getPatientsGreaterFifteenMiles,
  getPatientsGreaterFifteen,
  getAllTripsByDriverCenter,
  getNewTripsByCenter,
  getNewTrips,
  getSpecialTransp,
  getSpecialTranspByCenter,
  generateAllTrips,
  getKpiByCenter
}


