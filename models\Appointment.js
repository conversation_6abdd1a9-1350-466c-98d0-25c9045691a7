const { DataTypes } = require('sequelize');

const db = require('../db/connection');

const Appointment = db.define('ges_appoinments', {
  id: {
    type: DataTypes.BIGINT.UNSIGNED,
    autoIncrement: true,
    primaryKey: true
  },
  IdOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  IdMC: {
    type: DataTypes.BIGINT.UNSIGNED,
    defaultValue: 0
  },
  Time: {
    type: DataTypes.TIME,
    allowNull: true
  },
  Date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  LastName: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  FirstName: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  MiddleName: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  PatNumber: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  DOB: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  AddressPatient: {
    type: DataTypes.STRING(250),
    allowNull: true
  },
  City: {
    type: DataTypes.STRING(200),
    allowNull: true
  },
  State: {
    type: DataTypes.STRING(200),
    allowNull: true
  },
  ZipCode: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  PhoneNumber: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  MobilNumber: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  AddressDestination: {
    type: DataTypes.STRING(250),
    allowNull: true
  },
  ConsultDestination: {
    type: DataTypes.STRING(250),
    allowNull: true
  },
  special_requeriment: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  Driver: {
    type: DataTypes.STRING(200),
    allowNull: true
  },
  PhoneCompanion: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  Companion: {
    type: DataTypes.STRING(200),
    allowNull: true
  },
  TripType: {
    type: DataTypes.STRING(5),
    allowNull: true
  },
  ConfirmTrip: {
    type: DataTypes.TINYINT,
    defaultValue: 0
  },
  // OB: {
  //   type: DataTypes.TIMESTAMP,
  //   allowNull: true
  // },
  // RP: {
  //   type: DataTypes.TIMESTAMP,
  //   allowNull: true
  // },
  // CD: {
  //   type: DataTypes.TIMESTAMP,
  //   allowNull: true
  // },
  // OO: {
  //   type: DataTypes.TIMESTAMP,
  //   allowNull: true
  // },
  // RS: {
  //   type: DataTypes.TIMESTAMP,
  //   allowNull: true
  // },
  Cod_Cancell: {
    type: DataTypes.SMALLINT,
    allowNull: true
  },
  attention_type: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  outside_center_name: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  outside_center_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  outside_doctor_resource: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  outside_motive: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  notes: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  outside_motive_details: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  format_requeriment: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  distance: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true
  },
  distance_range: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  driver_id: {
    type: DataTypes.BIGINT.UNSIGNED,
    allowNull: true
  },
  KeepIt: {
    type: DataTypes.TINYINT,
    defaultValue: 0
  },
  notified_driver: {
    type: DataTypes.TINYINT,
    defaultValue: 0
  },
  latorig: {
    type: DataTypes.STRING(40),
    allowNull: true
  },
  lngorig: {
    type: DataTypes.STRING(40),
    allowNull: true
  },
  latdest: {
    type: DataTypes.STRING(40),
    allowNull: true
  },
  lngdest: {
    type: DataTypes.STRING(40),
    allowNull: true
  },
  dist: {
    type: DataTypes.STRING(40),
    allowNull: true
  },
  distkm: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  duration: {
    type: DataTypes.STRING(40),
    allowNull: true
  },
  durationmin: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  drivercolor: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  statusGoogle: {
    type: DataTypes.STRING(60),
    allowNull: true
  },
  return_time: {
    type: DataTypes.TIME,
    allowNull: true
  },
  comment: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  patient_id: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  DayPhone: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  HashTrip: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  resourcename1: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  resourcename2: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  confirmstatus: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  schstatus: {
    type: DataTypes.STRING(255),
    allowNull: true
  }
}, {
  timestamps: false
});

Appointment.associate = function(models) {
  Appointment.belongsTo(models.Driver, {
    foreignKey: 'driver_id',
    targetKey: 'Id'
  });
};

Appointment.associate = function (models) {
  MedicalCenter.hasMany(models.Patient, {
     foreignKey: 'PatNumber',
     targetKey: 'MedicalNumber'
  });
};

module.exports = { Appointment };

