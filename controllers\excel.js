const fs = require('fs');
const { request, response } = require("express");
const moment = require('moment');
const { MedicalCenter } = require("../models/MedicalCenter");
const { getPatientsGreaterFifteen, getAllTripsByDriverCenter, getNewTripsByCenter, getPatientsByMilesNew, getCancellations, getSpecialTranspByCenter, generateAllTrips } = require('./reporting');
const xlsx = require('node-xlsx').default;
const { Chart, registerables } = require('chart.js');
const ExcelJS = require('exceljs');
Chart.register(...registerables);
const { createCanvas } = require('canvas');

const generatePatientByMilesIntervalExcel = async (req = request, res = response) => {
  try {
    const totalPatientByMiles = await getPatientsByMilesNew(
      req.body.dateIni,
      req.body.dateEnd,
      Number(req.body.medicalCenterId),
      req.body.onlyMC
    );

    const data = {
      reportTimeout: moment(new Date()).format('MM/DD/YY hh:mm A'),
      totalPatients: totalPatientByMiles.totalPatients,
      medicalCenterName: totalPatientByMiles.medicalCenterName,
      dataArray: totalPatientByMiles.dataArray,
    };

    const excelData = [
      ['Report Timeout', data.reportTimeout],
      ['Medical Center Name', data.medicalCenterName],
      ['Total Patients', data.totalPatients],
      [],
      ['Interval', 'Total Patients', 'Percentage'],
    ];

    data.dataArray.forEach((item, index) => {
      excelData.push([
        item.interval,
        `${item.count}`,
        `${item.percentage}%`,
      ]);
      excelData.push([]);
      excelData.push(['', 'App Date', 'Patient Name', 'Home Address', 'Destination', 'Phone', 'Distance']);

      item.patients.forEach((patient, index) => {
        const counter = item.counters[index];
        excelData.push([
          '',
          patient.appDate,
          counter === 1 ? `${patient.name}` : `${patient.name} (${counter})`,
          patient.address,
          patient.destination,
          patient.phone,
          `${patient.distance}`,
        ]);
      });

      if(index < 4){
        excelData.push([]);
        excelData.push(['Interval', 'Total Patients', 'Percentage']);
      }

    });

    const buffer = xlsx.build([{ name: 'Patients Miles Interval', data: excelData }]);

    const filePath = './patients_miles_interval_report.xlsx';
    fs.writeFileSync(filePath, buffer);

    res.download(filePath, 'patients_miles_interval_report.xlsx', (err) => {
      if (err) {
        console.error('Error sending file:', err);
        return res.status(500).send('Error generating report');
      }
    });

    res.on('finish', () => {
      if (fs.existsSync(filePath)) {
        fs.unlink(filePath, (unlinkErr) => {
          if (unlinkErr) {
            console.error('Error deleting file:', unlinkErr);
          } else {
            console.log('Temporary file deleted successfully.');
          }
        });
      }
    });

  } catch (error) {
    console.error('Error generating patient by miles interval Excel:', error);
    res.status(500).send('Error generating Excel');
  }
};

const generatePatientGreaterFifteenMilesExcel = async ( req = request, res = response )  => {
  try {
    const patientsByMiles = await getPatientsGreaterFifteen(req.body.dateIni, req.body.dateEnd, Number(req.body.medicalCenterId));

    let medicalCenterName = '';
    const medicalCenter = (await MedicalCenter.findAll()).filter(medicalCenter => medicalCenter.IdMedicalC === Number(req.body.medicalCenterId));
    if (medicalCenter.length != 0) {
       medicalCenterName = medicalCenter[0].Name;
    }

    const excelData = [
      ['Report Timeout', moment(new Date()).format('MM/DD/YY hh:mm A')],
      ['Medical Center Name', medicalCenterName],
      ['Total Patients', `${patientsByMiles.totalPatients}`],
      ['Percentage', `${patientsByMiles.percentage}%`],
      ['Patients Greater than 15 Miles', `${patientsByMiles.dataArray.length}`],
      [],
      ['Name', 'Distance (Miles)'],
    ];

    patientsByMiles.dataArray.forEach(patient => {
      excelData.push([patient.name, patient.distance]);
    });


    const buffer = xlsx.build([{ name: 'Greater Fifteen Miles Patients', data: excelData }]);

    const filePath = './patients_greater_fifteen_miles_report.xlsx';
    fs.writeFileSync(filePath, buffer);

    res.download(filePath, 'patients_greater_fifteen_miles_report.xlsx', (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error generating report');
      }
      fs.unlinkSync(filePath);
    });

  } catch (error) {
    console.error('Error generating patients greater fifteen miles Excel: ', error);
    res.status(500).send('Error generating Excel');
  }
};

const generateAllTripsExcel = async (req = request, res = response) => {
  try {
    const allTripsData = await generateAllTrips(req.body.dateIni, req.body.dateEnd);

    const excelData = [
      ['Report Timeout', moment(new Date()).format('MM/DD/YY hh:mm A')],
      ['Date', allTripsData.queryDate],
      ['Total number of trips', allTripsData.totalTrips],
      ['Total number of completed trips', allTripsData.totalCompleted, `${allTripsData.completedPercentage}%`],
      ['Total number of cancelled trips', allTripsData.totalCancelled, `${allTripsData.cancelledPercentage}%`],
      ['Total number of pending trips', allTripsData.pendingTrips, `${allTripsData.pendingPercentage}%`],
      [],
      ['Medical Center', 'Driver Name', 'Scheduled', 'Completed', 'Completed (%)', 'Cancelled', 'Cancelled (%)', 'Pending', 'Pending (%)'],
    ];

    for (const medicalCenter in allTripsData.groupedTrips) {
      const centerData = allTripsData.groupedTrips[medicalCenter];
      excelData.push([medicalCenter, '', `Total: ${centerData.totalTrips}`, `Total: ${centerData.completedTrips}`, `${centerData.completedPercentage}%`, `Total: ${centerData.cancelledTrips}`, `${centerData.cancelledPercentage}%`, `Total: ${centerData.pendingTrips}`, `${centerData.pendingPercentage}%`]);

      for (const driver in centerData.drivers) {
        const driverData = centerData.drivers[driver].stats;
        excelData.push([null, driver, driverData.Scheduled, driverData.Completed, `${driverData.CompletedPercentage}%`, driverData.Cancelled, `${driverData.CancelledPercentage}%`, driverData.Pending, `${driverData.PendingPercentage}%`]);
      }

      excelData.push([]);
    }

    const buffer = xlsx.build([{ name: 'All Trips Report', data: excelData }]);

    const filePath = './all_trips_report.xlsx';
    fs.writeFileSync(filePath, buffer);

    res.download(filePath, 'all_trips_report.xlsx', (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error generating Excel');
      }
      fs.unlinkSync(filePath);
    });

  } catch (error) {
    console.error('Error generating all trips Excel: ', error);
    res.status(500).send('Error generating Excel');
  }
};

const generateTripsByDriverExcel = async ( req = request, res = response )  => {
  try {
    const trips = await getAllTripsByDriverCenter(req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest, Number(req.body.driverId));

    const sheets = [];

    trips.dataArray.forEach(centerGroup => {
       const sheetData = [
         ['Report Timeout', moment(new Date()).format('MM/DD/YY hh:mm A')],
         ['Date', trips.queryDate],
         ['Medical Center', centerGroup.medicalCenterName],
         ['Driver', trips.driverName],
         ['Zone', `${trips.zone}`],
         ['Vehicle', `${trips.vehicle}`],
         ['Total Trips', `${centerGroup.trips.length}`],
         ['Total Completed',`${trips.completedTrips}`],
         ['Total Not Completed',`${trips.totalTrips - trips.completedTrips}`],
         ['Effective Work',`${trips.effectiveWork}`],
         [],
         ['Trip Id', 'App Time', 'Patient Name', 'Phones', 'Pickup Location', 'Reason', 'Special Needs', 'Destination, On Board', 'Drop Off', 'Travel Time', 'Late Arrival'],
       ];

       centerGroup.trips.forEach((trip) => {
         sheetData.push([
           trip.tripId,
           trip.appTime,
           trip.patientName,
           trip.patientPhones,
           trip.pickupLocation,
           trip.reason,
           trip.specialNeeds,
           trip.destination,
           trip.onBoard,
           trip.dropOff,
           trip.travelTime,
           trip.lateArrival
         ]);
       });

       sheets.push({ name: centerGroup.medicalCenterName || 'Unknown', data: sheetData });
    });

    const buffer = xlsx.build(sheets);

    const filePath = './trips_by_driver_report.xlsx';
    fs.writeFileSync(filePath, buffer);

    res.download(filePath, 'trips_by_driver_report.xlsx', (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error generating report');
      }
      fs.unlinkSync(filePath);
    });

  } catch (error) {
    console.error('Error generating trips by driver Excel: ', error);
    res.status(500).send('Error generating Excel');
  }
};

const generateNewTripsByCenterExcel = async ( req = request, res = response )  => {
  try {
    const trips = await getNewTripsByCenter(Number(req.body.idCenter), req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);

    const sheets = [];

    trips.dataArray.forEach(driverGroup => {
      let totalTrips = Array.isArray(driverGroup.trips) ? driverGroup.trips.length : 0;
       const sheetData = [
        ['Report Timeout', moment(new Date()).format('MM/DD/YY hh:mm A')],
        ['Date', trips.queryDate],
        ['Medical Center', trips.medicalCenter],
         ['Driver', `${driverGroup.driverName}`],
         ['Zone', `${driverGroup.zone}`],
         ['Vehicle', `${driverGroup.vehicle}`],
         ['Total Trips', `${totalTrips}`],
         ['Total Completed',`${driverGroup.completedTrips}`],
         ['Total Cancelled',`${driverGroup.canceledTrips}`],
         ['Total Late',`${driverGroup.lateTrips}`],
         ['Effective Work',`${driverGroup.effectiveWork}`],
         [],
         ['Trip Id', 'App Time', 'Patient Name', 'Phones', 'Pickup Location', 'Reason', 'Special Needs', 'Destination', 'On Board', 'Drop Off', 'Travel Time', 'Late Arrival'],
       ];

       driverGroup.trips.forEach((trip) => {
         sheetData.push([
           trip.tripId,
           trip.appTime,
           trip.patientName,
           trip.patientPhones,
           trip.pickupLocation,
           trip.reason,
           trip.specialNeeds,
           trip.destination,
           trip.onBoard,
           trip.dropOff,
           trip.travelTime,
           trip.lateArrival
         ]);
       });
       sheets.push({ name: driverGroup.driverName || 'Unknown', data: sheetData });
    });

    const buffer = xlsx.build(sheets);

    const filePath = './new_trips_by_center_report.xlsx';
    fs.writeFileSync(filePath, buffer);

    res.download(filePath, 'new_trips_by_center_report.xlsx', (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error generating report');
      }
      fs.unlinkSync(filePath);
    });

  } catch (error) {
    console.error('Error generating new trips by center Excel: ', error);
    res.status(500).send('Error generating Excel');
  }
};

const generateCancellationsExcel = async (req = request, res = response) => {
  try {
    const cancellations = await getCancellations(Number(req.body.IdMedicalCenter),
    req.body.Driver,
    req.body.dateIni,
    req.body.dateEnd,
    req.body.isTodaysRequest,
    req.body.tripType
    );

    const sheets = [];

    cancellations.dataArray.forEach(driverGroup => {
       const sheetData = [
         ['Report Timeout', moment(new Date()).format('MM/DD/YY hh:mm A')],
         ['Date', cancellations.queryDate],
         ['Medical Center', cancellations.medicalCenter],
         ['Driver', driverGroup.driverName],
         ['Zone', `${driverGroup.zone}`],
         ['Vehicle', `${driverGroup.vehicle}`],
         ['Total Trips', `${driverGroup.trips.length}`],
         [],
         ['Trip Id', 'App Time', 'Driver Name', 'Patient Name', 'Phones', 'Comments', 'Home Address', 'Special Needs', 'Reason', 'Destination'],
       ];

       driverGroup.trips.forEach((trip) => {
         sheetData.push([
          `${trip.tripId}`,
           trip.appTime,
           driverGroup.driverName,
           trip.patientName,
           trip.patientPhones,
           trip.comments,
           trip.pickupLocation,
           trip.specialNeeds,
           trip.reason,
           trip.destination,
         ]);
       });

       sheets.push({ name: `${driverGroup.driverName}`, data: sheetData });
    });

    const buffer = xlsx.build(sheets);

    const filePath = './cancellations_report.xlsx';
    fs.writeFileSync(filePath, buffer);

    res.download(filePath, 'cancellations_report.xlsx', (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error generating report');
      }
      fs.unlinkSync(filePath);
    });
  }
  catch (error) {
    console.error('Error generating cancellations Excel: ', error);
    res.status(500).send('Error generating Excel');
  }
}

const generateSpecialTranspExcel = async (req = request, res = response) => {
  try {
    const trips = await getSpecialTranspByCenter(Number(req.body.medicalCenterId), Number(req.body.driverId), req.body.dateIni, req.body.dateEnd, req.body.isTodaysRequest);
    const sheets = [];

    trips.dataArray.forEach(driverGroup => {
      let totalTrips = Array.isArray(driverGroup.trips) ? driverGroup.trips.length : 0;
       const sheetData = [
        ['Report Timeout', moment(new Date()).format('MM/DD/YY hh:mm A')],
        ['Date', trips.queryDate],
        ['Medical Center', trips.medicalCenter],
         ['Driver', `${driverGroup.driverName}`],
         ['Zone', `${driverGroup.zone}`],
         ['Vehicle', `${driverGroup.vehicle}`],
         ['Total Trips', `${totalTrips}`],
         ['Total Completed',`${driverGroup.completedTrips}`],
         ['Total Cancelled',`${driverGroup.canceledTrips}`],
         ['Total Late',`${driverGroup.lateTrips}`],
         ['Effective Work',`${driverGroup.effectiveWork}`],
         [],
         ['Trip Id', 'App Time', 'Patient Name', 'Phones', 'Pickup Location', 'Reason', 'Special Needs', 'Destination', 'On Board', 'Drop Off', 'Travel Time', 'Late Arrival'],
       ];

       driverGroup.trips.forEach((trip) => {
         sheetData.push([
           trip.tripId,
           trip.appTime,
           trip.patientName,
           trip.patientPhones,
           trip.pickupLocation,
           trip.reason,
           trip.specialNeeds,
           trip.destination,
           trip.onBoard,
           trip.dropOff,
           trip.travelTime,
           trip.lateArrival
         ]);
       });
       sheets.push({ name: driverGroup.driverName || 'Unknown', data: sheetData });
    });


    const buffer = xlsx.build(sheets);

    const filePath = './special_transp_report.xlsx';
    fs.writeFileSync(filePath, buffer);

    res.download(filePath, 'special_transp_report.xlsx', (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error generating report');
      }
      fs.unlinkSync(filePath);
    });

  } catch (error) {
    console.error('Error generating special transport Excel: ', error);
    res.status(500).send('Error generating Excel');
  }
};

// const generateDailyTripPerformanceExcel = async (req, res) => {

//   try {
//     // Datos de entrada
//     const data = [
//       ['Driver', 'Date', 'Total Trips', 'Completed', 'Not Completed', 'Effective Work (%)'],
//       ['Driver 1', '12/18/2024', 30, 10, 20, 33.3],
//       ['Driver 2', '12/18/2024', 50, 20, 30, 40],
//       ['Driver 3', '12/18/2024', 70, 15, 55, 21.4],
//       ['Driver 1', '12/19/2024', 80, 5, 75, 6.3],
//       ['Driver 2', '12/19/2024', 100, 0, 100, 0],
//     ];

//     // Crear el gráfico
//     function createChart(data) {
//       const labels = data.map(row => `${row[0]} (${row[1]})`);
//       const completed = data.map(row => row[3]);
//       const notCompleted = data.map(row => row[4]);
//       const effectiveWork = data.map(row => row[5]);

//       const canvas = createCanvas(800, 400);
//       const ctx = canvas.getContext('2d');

//       new Chart(ctx, {
//         type: 'bar',
//         data: {
//           labels,
//           datasets: [
//             {
//               label: 'Completed',
//               data: completed,
//               backgroundColor: 'rgba(255, 206, 86, 0.8)', // Amarillo
//             },
//             {
//               label: 'Not Completed',
//               data: notCompleted,
//               backgroundColor: 'rgba(255, 99, 132, 0.8)', // Rojo
//             },
//             {
//               label: 'Effective Work (%)',
//               data: effectiveWork,
//               type: 'line',
//               borderColor: 'rgba(75, 192, 192, 1)', // Verde agua
//               yAxisID: 'y1',
//             },
//           ],
//         },
//         options: {
//           responsive: false,
//           scales: {
//             y: { beginAtZero: true, title: { display: true, text: 'Trips' } },
//             y1: { beginAtZero: true, position: 'right', title: { display: true, text: 'Effective Work (%)' } },
//           },
//           plugins: {
//             legend: { position: 'top' },
//             title: { display: true, text: 'Daily Trip Performance by Driver' },
//           },
//         },
//       });

//       return canvas.toBuffer('image/png');
//     }

//     const chartBuffer = createChart(data.slice(1));

//     // Crear un nuevo workbook
//     const workbook = new ExcelJS.Workbook();
//     const worksheet = workbook.addWorksheet('Data');

//     // Agregar datos a la hoja
//     data.forEach((row, index) => {
//       worksheet.addRow(row);
//     });

//     // Insertar la imagen del gráfico
//     const imageId = workbook.addImage({
//       buffer: chartBuffer,
//       extension: 'png',
//     });

//     worksheet.addImage(imageId, {
//       tl: { col: 0, row: data.length + 2 }, // Posición de la imagen (columna y fila)
//       ext: { width: 800, height: 400 }, // Tamaño de la imagen
//     });

//     // Exportar el archivo Excel
//     const filePath = './trips_by_driver_report.xlsx';
//     await workbook.xlsx.writeFile(filePath);

//     // Enviar el archivo como respuesta
//     res.download(filePath, 'trips_by_driver_report.xlsx', (err) => {
//       if (err) {
//         console.error('Error sending file:', err);
//         res.status(500).send('Error generating report');
//       }
//       fs.unlinkSync(filePath); // Eliminar el archivo después de enviarlo
//     });
//   } catch (error) {
//     console.error('Error generating trips by driver Excel: ', error);
//     res.status(500).send('Error generating Excel');
//   }
// };

module.exports = { generatePatientByMilesIntervalExcel, generatePatientGreaterFifteenMilesExcel, generateAllTripsExcel, generateTripsByDriverExcel, generateNewTripsByCenterExcel, generateCancellationsExcel, generateSpecialTranspExcel };