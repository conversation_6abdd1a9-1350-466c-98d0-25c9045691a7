
const { request, response } = require('express');
const { User } = require('./../models/User');
const { generateToken } = require('../helpers/jwt');

const userLogin = async (req = request, res = response) => {
  const { email, password } = req.body;

  const user = await User.findOne({ where: {
    email,
    password
  } });

  if (!user) {
    return res.status(401).json({
      msg: 'Not authorized.'
    });
  }

  const token = await generateToken(user.id, user.email);

  return res.status(200).json({
    id: user.id,
    email: user.email,
    token
  });
}


module.exports = {
  userLogin
}