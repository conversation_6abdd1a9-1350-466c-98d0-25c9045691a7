
// reporting routes
const { Router } = require('express');
const router = Router();
const {
  getMedicalCenters,
  getDrivers,
  getTripsByDriver,
  getAllCancellations,
  getAllTrips,
  getKpi,
  getPatientHistory,
  getAllPatients,
  getTripsByDriverCenter,
  getPatientsByMilesInterval,
  getPatientsByMilesIntervalNew,
  getPatientsGreaterFifteenMiles,
  getDailyTripPerformance,
  getNewTrips,
  getSpecialTransp } = require('../controllers/reporting');


const jwtValidator = require('../middlewares/jwt-validator');

// check-health
router.get('/check-health', (req, res) => {
  return res.status(200).json({
    ok: true
  })
})

// medical centers
router.get(
  '/get-medical-centers',
  // jwtValidator,
  getMedicalCenters
);

// drivers
router.get(
  '/get-drivers',
  // jwtValidator,
  getDrivers
);

// trips by driver
router.post(
  '/get-trips-by-driver',
  // jwtValidator,
  getTripsByDriver
  // getTripsBetweenDates
);

// trips by driver by center
router.post(
  '/get-trips-by-driver-center',
  // jwtValidator,
  getTripsByDriverCenter

);

// cancellations
router.post(
  '/get-cancellations',
  // jwtValidator,
  getAllCancellations
);

// all trips
router.post(
  '/get-all-trips',
  // jwtValidator,
  getAllTrips
);

// kpi
router.post(
  '/get-kpi',
  // jwtValidator,
  getKpi
);

// all patients
router.get(
  '/all-patients',
  getAllPatients
);

// patient history
router.post(
  '/patient-history',
  getPatientHistory
);

// Patients-by-miles-interval-new
router.post(
  '/get-patients-by-miles-interval-new',
  getPatientsByMilesIntervalNew
);

// Patients-by-miles-interval
router.post(
  '/get-patients-by-miles-interval',
  getPatientsByMilesInterval
);

// Patients-by-miles-greater-fifteen
router.post(
  '/get-patients-greater-fifteen-miles',
  getPatientsGreaterFifteenMiles
);

// get-new-trips
router.post(
  '/get-new-trips',
  // jwtValidator,
  getNewTrips
);

// get-special-transp
router.post(
  '/get-special-transp',
  // jwtValidator,
  getSpecialTransp
);

//get-daily-performance
router.post("/get-daily-performance", getDailyTripPerformance);

module.exports = router;

