
const express = require('express');
const db = require('./db/connection');
const cors = require('cors');

// express server
const app = express();

// cors
app.use( cors() );

// body parsing
app.use( express.json() );

// routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/reporting', require('./routes/reporting'));
app.use('/api/pdf', require('./routes/pdf'));
app.use('/api/excel', require('./routes/excel'));


const dbConnection = async () => {
  try {
    await db.authenticate();
    console.log('Db connected.');
  } catch (error) {
    console.log('Db connection error: ' + error);
  }
}

// db connection
dbConnection();

// listen
app.listen( process.env.PORT, () => {
  console.log(`Server running at: ${ process.env.PORT }`);
} )