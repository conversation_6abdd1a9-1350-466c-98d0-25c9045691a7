
const { Router } = require('express');
const router = Router();
const {
  generatePatientByMilesIntervalPdf,
  generatePatientGreaterFifteenMilesPdf,
  generateTripsByDriverCenterPdf,
  generateNewTripsByCenterPdf,
  generateCancellationsPdf,
  generateSpecialTranspPdf,
  generateAllTripsPdf,
  generateKpiPdf
} = require('../controllers/pdf');

router.post('/patient-by-miles-interval-pdf', generatePatientByMilesIntervalPdf);
router.post('/patient-greater-fifteen-miles-pdf', generatePatientGreaterFifteenMilesPdf);
router.post('/trips-by-driver-pdf', generateTripsByDriverCenterPdf);
router.post('/new-trips-by-center-pdf', generateNewTripsByCenterPdf);
router.post('/special-transp-pdf', generateSpecialTranspPdf);
router.post('/cancellations-pdf', generateCancellationsPdf);
router.post('/all-trips-pdf', generateAllTripsPdf);
router.post('/kpi-pdf', generateKpiPdf);
module.exports = router;