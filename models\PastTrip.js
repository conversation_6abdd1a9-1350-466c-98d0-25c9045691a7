

const { DataTypes } = require('sequelize');
const db = require('../db/connection');

const PastTrip = db.define('appoinments', {
  createdAt: false,
  updatedAt: false,
  IdMC: {
    type: DataTypes.INTEGER
  },
  Date: {
    type: DataTypes.DATE
  },
  CD: {
    type: DataTypes.DATE
  },
  OB: {
    type: DataTypes.DATE
  },
  RP: {
    type: DataTypes.DATE
  },
  Time: {
    type: DataTypes.TIME
  },
  FirstName: {
    type: DataTypes.STRING
  },
  AddressPatient: {
    type: DataTypes.STRING
  },
  PhoneNumber: {
    type: DataTypes.STRING
  },
  MobilNumber: {
    type: DataTypes.STRING
  },
  AddressDestination: {
    type: DataTypes.STRING
  },
  ConsultDestination: {
    type: DataTypes.STRING
  },
  Driver: {
    type: DataTypes.STRING
  },
  TripType: {
    type: DataTypes.STRING
  },
  driver_id: {
    type: DataTypes.INTEGER
  },
  special_requeriment: {
    type: DataTypes.TEXT
  },
  comment: {
    type: DataTypes.STRING
  }});

module.exports = { 
  PastTrip
}

