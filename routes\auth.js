
// auth routes
const { Router } = require('express');
const router = Router();
const { check } = require('express-validator');
const { fieldValidator } = require('./../middlewares/field-validator');

const { userLogin } = require('../controllers/auth');

router.post(
  '/login', 
  [
    check('email', 'Email is required.').not().isEmpty(),
    check('password', 'Password is required.').not().isEmpty(),
    fieldValidator
  ],
  userLogin
);


module.exports = router;