
const { Router } = require('express');
const router = Router();
const { generatePatientByMilesIntervalExcel, generatePatientGreaterFifteenMilesExcel, generateAllTripsExcel, generateTripsByDriverExcel, generateNewTripsByCenterExcel, generateCancellationsExcel, generateSpecialTranspExcel } = require('../controllers/excel');

router.post('/patient-by-miles-interval-xlsx', generatePatientByMilesIntervalExcel);
router.post('/patient-greater-fifteen-miles-xlsx', generatePatientGreaterFifteenMilesExcel);
router.post('/all-trips-xlsx', generateAllTripsExcel);
router.post('/trips-by-driver-xlsx', generateTripsByDriverExcel);
router.post('/new-trips-by-center-xlsx', generateNewTripsByCenterExcel);
router.post('/special-transp-xlsx', generateSpecialTranspExcel);
router.post('/cancellations-xlsx', generateCancellationsExcel);

module.exports = router;