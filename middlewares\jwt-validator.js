
const { request, response } = require('express');
const jwt = require('jsonwebtoken');

const jwtValidator = (req = request, res = response, next) => {

  const token = req.header('x-token');
  if (!token) {
    return res.status(401).json({
      msg: 'Api token required.'
    })
  }

  try {

    const payload = jwt.verify(
      token,
      process.env.SECRET_JWT_SEED
    )

    req.id = payload.id;
    req.username = payload.username;

  } catch (error) {
    return res.status(401).json({
      msg: 'Invalid api token.'
    });
  }

  next();

}

module.exports = jwtValidator;