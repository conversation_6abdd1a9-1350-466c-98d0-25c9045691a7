

const { DataTypes } = require('sequelize');
const db = require('../db/connection');

const MedicalCenter = db.define('medical_center', {
  createdAt: false,
  updatedAt: false,
  IdMedicalC: {
    type: DataTypes.STRING,
    primaryKey: true
  },
  Name: {
    type: DataTypes.STRING
  },
  AddressMedicalC: {
    type: DataTypes.STRING
  },
  Specialty: {
    type: DataTypes.STRING
  },
});
MedicalCenter.associate = function (models) {
  MedicalCenter.hasMany(models.Driver, {
    foreignKey: 'IdMC',
    targetKey: 'Id'
  });
};
module.exports = { 
  MedicalCenter 
}
