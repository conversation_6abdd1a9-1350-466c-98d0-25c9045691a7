const { DataTypes } = require('sequelize');
const db = require('../db/connection');

const DriverMC = db.define('driversmedicalc', {
  createdAt: false,
  updatedAt: false,
  IdDrv: {
    type: DataTypes.INTEGER,
    primaryKey: true
  },
  IdMC: {
    type: DataTypes.INTEGER,
    primaryKey: true
  },
  Region: {
    type: DataTypes.STRING
  },
  pcolor: {
    type: DataTypes.STRING
  },

});
//@HasMany (() => Driver,'Id');
DriverMC.associate = function (models) {
  DriverMC.hasMany(models.Driver, {
    foreignKey: 'Id',
    targetKey: 'IdDrv'
  });
};
module.exports = { 
  DriverMC 
}

