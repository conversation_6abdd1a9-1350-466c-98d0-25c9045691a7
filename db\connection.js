
const { Sequelize } = require('sequelize');
require('dotenv').config();


const db = new Sequelize(
  process.env.DB,
  process.env.DBUSER,
  process.env.DBPASS,

  {
    host: 'localhost',
    dialect: 'mysql',
    timezone: '-05:00',
    define: {
    defaultScope: {
      attributes: { exclude: ['created_at','updated_at',
      'createdAt', 'updatedAt'] }
     },
    timestamps: false
    }
   }
);

module.exports = db;