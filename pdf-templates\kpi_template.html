<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{reportTop.title}}</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
      }

      .container {
        width: 100%;
        padding: 20px;
        text-align: center;
        box-sizing: border-box;
      }

      .header-section {
        position: relative;
        margin-bottom: 20px;
      }

      .header {
        font-size: 18px;
        font-weight: bold;
        margin: 10px 0;
        text-align: center;
      }

      .date-section {
        position: absolute;
        top: 0;
        right: 0;
        text-align: right;
      }

      .date-section p {
        margin: 0;
        font-weight: bold;
      }

      .divider {
        background-color: #79a7e3;
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }
      
      .info-divider { 
        padding: 10px;
        display: flex;
        justify-content: space-around; 
        align-items: center;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f9f9f9;
        flex-wrap: wrap;
      }

      .info-group {
        display: flex;
        align-items: center;
        gap: 5px;
        margin: 5px 10px;
      }

      p {
        margin: 0;
        padding: 0;
        padding-top: 3px;
        color: rgb(49, 71, 103);
      }
      
      .info-divider p { 
         color: #333;
      }
      
      .info-divider .header-value { 
        background-color: #fff;
        border: 1px solid #ccc;
        color: #000;
      }


      .header-value {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 2px;
        background-color: #fff;
        font-weight: bold;
        color: #000;
        min-width: 30px;
        text-align: center;
      }

      .date-time {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 4px;
        background-color: rgba(255, 255, 153, 255);
        color: #000;
      }

      .location {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 4px;
        background-color: rgba(230, 237, 215, 255);
        font-weight: bold;
        color: #000;
      }

      .table-container {
        width: 100%;
        overflow-x: auto;
        margin-top: 20px;
        margin-bottom: 20px;
      }

      .table {
        width: 100%;
        border-collapse: collapse;
        table-layout: auto; 
        margin: 0 auto;
        border-spacing: 0;
      }

      .table th {
        padding: 6px 4px; 
        text-align: center;
        color: #79a7e3;
        font-weight: bold;
        border-bottom: 1px solid #ddd;
        white-space: nowrap;
        font-size: 12px;
        vertical-align: top;
      }

      .table td {
        padding: 6px 4px; 
        text-align: center;
        border-bottom: 1px solid #ddd;
        font-size: 11px;
        word-wrap: break-word;
      }

      .table-header-row { 
        background-color: #f9f9f9; 
      }
      
      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #79a7e3; 
        margin: 25px 0 15px 0;
        text-align: center;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
      }

      .chart-container-wrapper { 
        margin-top: 20px;
      }
      
      .chart-container {
        display: flex;
        justify-content: space-around; 
        flex-wrap: wrap; 
        margin-bottom: 20px;
      }

      .chart {
        width: 48%; 
        min-width: 300px; 
        height: auto; 
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 15px; 
        box-sizing: border-box;
      }

      .chart-title {
        text-align: center;
        font-weight: bold;
        font-size: 13px;
        margin-bottom: 10px;
        color: #333; 
      }

      .chart-legend {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 10px;
        font-size: 10px;
      }

      .legend-item {
        display: flex;
        align-items: center;
        margin: 2px 8px; 
      }

      .legend-color {
        width: 12px; 
        height: 12px; 
        margin-right: 5px;
      }

      .color-transit { background-color: #8FE3CF; }
      .color-pending { background-color: #FFEA11; }
      .color-completed { background-color: #A10035; }
      .color-cancelled { background-color: #80558C; }

      .footer {
        margin-top: 30px;
        font-size: 10px;
        color: #555;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header-section">
        <div class="header">SACS Report Key Performance Indicators</div>
        <div class="date-section">
          <p>Date: {{reportQueryDate}}</p>
        </div>
      </div>

      <div class="divider">
        <span class="location">{{reportTop.medical_center}}</span>
        <div class="info-group">
          <p>Total Trips:</p>
          <span class="header-value">{{reportTop.total_trips}}</span>
        </div>
        <span class="date-time">{{reportTimeout}}</span>
      </div>

      <div class="info-divider">
        <div class="info-group">
          <p>Completed:</p>
          <span class="header-value">{{reportTop.completed.total}} ({{reportTop.completed.percentage}})</span>
        </div>
        <div class="info-group">
          <p>On Time:</p>
          <span class="header-value">{{reportTop.onTime.on_time.total}} ({{reportTop.onTime.on_time.percentage}}%)</span>
        </div>
        <div class="info-group">
          <p>Late:</p>
          <span class="header-value">{{reportTop.onTime.late.total}} ({{reportTop.onTime.late.percentage}}%)</span>
        </div>
      </div>

      <div class="table-container">
        <table class="table">
          <thead>
            <tr class="table-header-row">
              <th>Type</th>
              <th>Patient Name</th>
              <th>Appointment</th>
              <th>On Board</th>
              <th>Drop Off</th>
              <th>Driver</th>
              <th>On Time</th>
              <th>Trip Time (min)</th>
            </tr>
          </thead>
          <tbody>
            {{#each reportItems}}
            <tr>
              <td>{{this.trip_type}}</td>
              <td>{{this.patient_name}}</td>
              <td>{{this.appointment_date}}</td>
              <td>{{this.ob}}</td>
              <td>{{this.dp}}</td>
              <td>{{this.driver}}</td>
              <td>{{this.on_time}}</td>
              <td>{{this.trip_time}}</td>
            </tr>
            {{/each}}
          </tbody>
        </table>
      </div>

      {{#if reportReturnItems.length}}
      <div class="section-title">Return Trips</div>
      <div class="table-container">
        <table class="table">
          <thead>
            <tr class="table-header-row">
              <th>Patient Name</th>
              <th>On Board</th>
              <th>Drop Off</th>
              <th>Driver</th>
            </tr>
          </thead>
          <tbody>
            {{#each reportReturnItems}}
            <tr>
              <td>{{this.patient_name}}</td>
              <td>{{this.ob}}</td>
              <td>{{this.dp}}</td>
              <td>{{this.driver}}</td>
            </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
      {{/if}}

      {{#if chartData}} <!-- Check if chartData exists -->
      <div class="chart-container-wrapper">
        <div class="section-title">Trip Statistics</div>
        <div class="chart-container">
          <div class="chart">
            <div class="chart-title">Type A Trips</div>
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-color color-transit"></div>
                <span>Transit: {{chartData.[0].[1]}}</span>
              </div>
              <div class="legend-item">
                <div class="legend-color color-pending"></div>
                <span>Pending: {{chartData.[1].[2]}}</span>
              </div>
              <div class="legend-item">
                <div class="legend-color color-completed"></div>
                <span>Completed: {{chartData.[2].[3]}}</span>
              </div>
              <div class="legend-item">
                <div class="legend-color color-cancelled"></div>
                <span>Cancelled: {{chartData.[3].[4]}}</span>
              </div>
            </div>
          </div>
          
          {{#if chartBData}}
          <div class="chart">
            <div class="chart-title">Type B Trips</div>
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-color color-transit"></div>
                <span>Transit: {{chartBData.[0].[1]}}</span>
              </div>
              <div class="legend-item">
                <div class="legend-color color-pending"></div>
                <span>Pending: {{chartBData.[1].[2]}}</span>
              </div>
              <div class="legend-item">
                <div class="legend-color color-completed"></div>
                <span>Completed: {{chartBData.[2].[3]}}</span>
              </div>
              <div class="legend-item">
                <div class="legend-color color-cancelled"></div>
                <span>Cancelled: {{chartBData.[3].[4]}}</span>
              </div>
            </div>
          </div>
          {{/if}}
        </div>
      </div>
      {{/if}}

      <div class="footer">
        <p>* Indicates trips with duration over 45 minutes</p>
      </div>
    </div>
  </body>
</html>
