const { DataTypes } = require('sequelize');

const db = require('../db/connection');

const Patient = db.define('patients', {
  createdAt: false,
  updatedAt: false,
  Id: {
    type: DataTypes.INTEGER
  },
  IdPatient: {
    type: DataTypes.STRING
  },
  MedicalNumber: {
    type: DataTypes.STRING
  },
  Names: {
    type: DataTypes.STRING
  },
  BOD: {
    type: DataTypes.DATE
  },
  NumberPhone1: {
    type: DataTypes.STRING
  },
  NumberPhone2: {
    type: DataTypes.STRING
  },
  PatientAddress: {
    type: DataTypes.STRING
  },
  Email: {
    type: DataTypes.STRING
  },
  ContactPreference: {
    type: DataTypes.STRING
  },
  PhysicalLimits: {
    type: DataTypes.STRING
  },
  IdMedicalC: {
    type: DataTypes.INTEGER
  },
  ContactPerson: {
    type: DataTypes.STRING
  },
  PreferredLanguage: {
    type: DataTypes.STRING
  },
  driver: {
    type: DataTypes.INTEGER
  },
  patient_types: {
    type: DataTypes.INTEGER
  },
  Notes: {
    type: DataTypes.STRING
  },
  // created_at: {
  //   type: DataTypes.DATE
  // },
  // updated_at: {
  //   type: DataTypes.DATE
  // },
  format_requeriment: {
    type: DataTypes.STRING
  }
});

Patient.associate = function (models) {
  MedicalCenter.hasMany(models.Appointment, {
   foreignKey: 'PatNumber',
   sourceKey: 'MedicalNumber'
  });
};

module.exports = Patient;