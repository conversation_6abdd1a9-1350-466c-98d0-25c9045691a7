
const jwt = require('jsonwebtoken');

const generateToken = ( id, username ) => {
  
  return new Promise((resolve, reject) => {
    const payload = { id, username };
    
    jwt.sign( payload, 
      process.env.SECRET_JWT_SEED, 
      {
        expiresIn: '2h'
      },
      (error, token) => {
        if (error) {
          console.log(error);
          reject('Token error.');
        }
        resolve( token );
      }
    )
  });
}


module.exports = {
  generateToken
}