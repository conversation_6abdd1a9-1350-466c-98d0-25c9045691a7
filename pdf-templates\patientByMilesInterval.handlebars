<!DOCTYPE html>
<html lang="es">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Patients By Miles Interval</title>
	<style>
		body {
			font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            padding: 20px 20px 20px 20px;
            text-align: center;
            box-sizing: border-box;
        }
        .header {
            font-size: 20px;
            font-weight: bold;
            margin: 10px 0;
        }
        .divider {
            background-color: #79a7e3;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        p {
            margin: 0;
            padding: 0;
            font-size: medium;
        }
        .location {
            padding: 5px 10px;
            margin-left: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(230, 237, 215, 255);
            font-weight: bold;
            color: #000;
        }
        .date-time {
            padding: 5px 10px;
            margin-right: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(255, 255, 153, 255);
            color: #000;
        }
        .table {
            width: 100%;
            max-width: 600px; 
            padding-top: 30px;
            margin: 10px auto; 
            border-collapse: separate;
            border-spacing: 5px;
        }
        .table th, .table td {
            border: none;
        }
        .table-header th {
            border: none;
            color: #79a7e3;
        }
        .table-data td {
            padding: 8px;
            text-align: center;
            border: none;
        }
        .table-data td:nth-child(2), .table-data td:nth-child(3) {
            border: 1px solid #ccc; 
        }
        .table-data td:nth-child(1) {
            text-align: left;
            padding-left: 10px;
            color: #79a7e3;
        }
        .last-row td:nth-child(1) {
            font-weight: bold;
        }
        .last-row td:nth-child(2) {
            border: 1px solid #ccc; 
            font-weight: bold;
        }
        .last-row td:nth-child(3) {
            border: none; 
        }
        .spacer-row td {
            height: 20px;
        }
	</style>
</head>
<body>
	<div class="container">
        <div class="header">SACS Report Patients By Miles Interval</div>
		<div class="divider">
			<span class="location">{{medicalCenterName}}</span>
			<span class="date-time">
				<p>{{reportTimeout}}</p>
			</span>
		</div>
		<table class="table">
			<tr class="table-header">
				<th></th>
				<th></th>
				<th>% Percentage</th>
			</tr>
			<tr class="table-data">
				<td>{{dataByMiles.0to2Miles.interval}}</td>
				<td>{{dataByMiles.0to2Miles.count}}</td>
				<td>{{dataByMiles.0to2Miles.percentage}}</td>
				</tr>
			<tr class="table-data">
				<td>{{dataByMiles.2to5Miles.interval}}</td>
				<td>{{dataByMiles.2to5Miles.count}}</td>
				<td>{{dataByMiles.2to5Miles.percentage}}</td>
				</tr>
			<tr class="table-data">
				<td>{{dataByMiles.5to10Miles.interval}}</td>
				<td>{{dataByMiles.5to10Miles.count}}</td>
				<td>{{dataByMiles.5to10Miles.percentage}}</td>
				</tr>
			<tr class="table-data">
				<td>{{dataByMiles.10to15Miles.interval}}</td>
				<td>{{dataByMiles.10to15Miles.count}}</td>
				<td>{{dataByMiles.10to15Miles.percentage}}</td>
				</tr>
			<tr class="table-data">
				<td>{{dataByMiles.Greater15Miles.interval}}</td>
				<td>{{dataByMiles.Greater15Miles.count}}</td>
				<td>{{dataByMiles.Greater15Miles.percentage}}</td>
				</tr>
			<tr class="spacer-row">
				<td colspan="3"></td>
			</tr>
			<tr class="table-data last-row">
				<td>Total Patients</td>
				<td>{{totalPatients}}</td>
				<td></td>
			</tr>
		</table>
	</div>
</body>
</html>
