

const { DataTypes } = require('sequelize');
const db = require('../db/connection');

const Trip = db.define('ges_appoinments', {
  createdAt: false,
  updatedAt: false,
  IdMC: {
    type: DataTypes.INTEGER
  },
  Date: {
    type: DataTypes.DATE
  },
  CD: {
    type: DataTypes.DATE
  },
  OB: {
    type: DataTypes.DATE
  },
  RP: {
    type: DataTypes.DATE
  },
  Time: {
    type: DataTypes.TIME
  },
  FirstName: {
    type: DataTypes.STRING
  },
  LastName: {
    type: DataTypes.STRING
  },
  patient_id: {
    type: DataTypes.INTEGER
  },
  patNumber: {
    type: DataTypes.STRING
  },  
  AddressPatient: {
    type: DataTypes.STRING
  },
  PhoneNumber: {
    type: DataTypes.STRING
  },
  MobilNumber: {
    type: DataTypes.STRING
  },
  AddressDestination: {
    type: DataTypes.STRING
  },
  ConsultDestination: {
    type: DataTypes.STRING
  },
  Driver: {
    type: DataTypes.STRING
  },
  TripType: {
    type: DataTypes.STRING
  },
  driver_id: {
    type: DataTypes.INTEGER
  },
  special_requeriment: {
    type: DataTypes.TEXT
  },
  notes: {
    type: DataTypes.STRING
  },
  outside_motive_details: {
    type: DataTypes.STRING
  },
  comment: {
    type: DataTypes.STRING
  },
  resourcename1: {
    type: DataTypes.STRING
  },
  resourcename2: {
    type: DataTypes.STRING
  },
  confirmstatus: {
    type: DataTypes.STRING
  },
  Cod_Cancell: {
    type: DataTypes.STRING
  },
});

module.exports = { 
  Trip
}


