<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cancellations Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            padding: 20px;
            text-align: center;
            box-sizing: border-box;
        }
        .header-section {
            position: relative;
            margin-bottom: 20px;
        }
        .header {
            font-size: 18px;
            font-weight: bold;
            margin: 10px 0;
            text-align: center;
        }
        .date-section {
            position: absolute;
            top: 0;
            right: 0;
            text-align: right;
        }
        .date-section p {
            margin: 0;
            font-weight: bold;
        }
        .divider {
            background-color: #79a7e3;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .info-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        p {
            margin: 0;
            padding: 0;
            padding-top: 3px;
            color: rgb(49, 71, 103);
        }
        .header-value {
            padding: 5px 10px;
            border: 1px solid #000;
            border-radius: 2px;
            background-color: #fff;
            font-weight: bold;
            color: #000;
            min-width: 30px;
            text-align: center;
        }
        .date-time {
            padding: 5px 10px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(255, 255, 153, 255);
            color: #000;
        }
        .location {
            padding: 5px 10px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(230, 237, 215, 255);
            font-weight: bold;
            color: #000;
        }
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        .table th {
            padding: 8px 4px;
            text-align: center;
            color: #79a7e3;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
            white-space: nowrap;
            font-size: 12px;
        }
        .table td {
            padding: 8px 4px;
            text-align: center;
            border-bottom: 1px solid #ddd;
            font-size: 11px;
            word-wrap: break-word;
            max-width: 150px;
        }
        .table-header {
            background-color: #f9f9f9;
        }
        .trip-id-col { width: 5%; }
        .app-time-col { width: 10%; }
        .driver-name-col { width: 10%; }
        .patient-name-col { width: 12%; }
        .comments-col { width: 13%; }
        .address-col { width: 15%; }
        .special-needs-col { width: 10%; }
        .destination-col { width: 20%; }
        
        .summary-section {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            background-color: #79a7e3;
            padding: 10px;
        }
        .summary-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .undefined-cell {
            font-style: italic;
            color: #777;
        }
        .driver-section {
            page-break-after: always;
        }
        .reason-row td {
            text-align: left;
            background-color: #f9f9f9;
            padding-left: 0;
            border-bottom: 1px solid #ddd;
        }
        .reason-content {
            padding-left: 0;
            display: grid;
            grid-template-columns: 5% 95%;
        }
        .reason-empty {
            /* Empty cell space for Trip ID column */
        }
        .reason-text {
            text-align: left;
            padding-left: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header-section">
            <div class="header">SACS Report Cancellations</div>
            <div class="date-section">
                <p>Date: {{reportQueryDate}}</p>
            </div>
        </div>
        
        <div class="divider">
            <span class="location">{{medicalCenter}}</span>
            <div class="info-group">
              <p>Total Number Of Trips:</p><span class="header-value">{{totalTrips}}</span>
            </div>
            <span class="date-time">
                {{reportTimeout}}
            </span>
        </div>
        
        {{#each dataArray}}
        <div class="driver-section">
            <div class="divider">
                <span class="location">{{driverName}}</span>
                <div class="info-group">
                  <p>Zone: </p><span class="header-value">{{zone}}</span>
                </div>
                <div class="info-group">
                  <p>Vehicle: </p><span class="header-value">{{vehicle}}</span>
                </div>
                <div class="info-group">
                  <p>Total Trips:</p><span class="header-value">{{trips.length}}</span>
                </div>
            </div>
            
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr class="table-header">
                            <th class="trip-id-col">Trip Id</th>
                            <th class="app-time-col">App Time</th>
                            <th class="driver-name-col">Driver Name</th>
                            <th class="patient-name-col">Patient Name</th>
                            <th class="comments-col">Comments</th>
                            <th class="address-col">Home Address</th>
                            <th class="special-needs-col">Special Needs</th>
                            <th class="destination-col">Destination</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each trips}}
                        <tr>
                            <td>{{tripId}}</td>
                            <td>
                                {{appTime}}
                                <br>
                                <strong>Phones: </strong>{{patientPhones}}
                            </td>
                            <td>{{../driverName}}</td>
                            <td>{{patientName}}</td>
                            <td>{{comments}}</td>
                            <td>{{pickupLocation}}</td>
                            <td>{{specialNeeds}}</td>
                            <td>{{destination}}</td>
                        </tr>
                        {{#if reason}}
                            {{#ifNotEquals reason "-"}}
                            <tr class="reason-row">
                                <td colspan="8">
                                    <div class="reason-content">
                                        <div class="reason-empty"></div>
                                        <div class="reason-text"><strong>Reason: </strong>{{reason}}</div>
                                    </div>
                                </td>
                            </tr>
                            {{/ifNotEquals}}
                        {{/if}}
                        {{/each}}
                    </tbody>
                </table>
            </div>
        </div>
        {{/each}}
    </div>
</body>
</html>