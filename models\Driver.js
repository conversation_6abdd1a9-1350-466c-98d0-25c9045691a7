const { DataTypes } = require('sequelize');
const db = require('../db/connection');

const Driver = db.define('driver_assigments', {
  createdAt: false,
  updatedAt: false,
  Id: {
    type: DataTypes.INTEGER,
    primaryKey: true
  },
  dZone: {
    type: DataTypes.INTEGER
  },
  IdMC: {
    type: DataTypes.INTEGER
  },
  IdVehicle: {
    type: DataTypes.INTEGER
  },
  Driver: {
    type: DataTypes.STRING
  },
  Phone1: {
    type: DataTypes.STRING
  },
  Address: {
    type: DataTypes.STRING
  },
  pcolor: {
    type: DataTypes.STRING
  },

});
Driver.associate = function (models) {
  Driver.BelongsTo(models.DriverMC, {
    foreignKey: 'IdDrv',
    targetKey: 'Id'
  });
};


module.exports = { 
  Driver 
}

