<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special Transport Report</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
      }

      .container {
        width: 100%;
        padding: 20px;
        text-align: center;
        box-sizing: border-box;
      }

      .header-section {
        position: relative;
        margin-bottom: 20px;
      }

      .header {
        font-size: 18px;
        font-weight: bold;
        margin: 10px 0;
        text-align: center;
      }

      .date-section {
        position: absolute;
        top: 0;
        right: 0;
        text-align: right;
      }

      .date-section p {
        margin: 0;
        font-weight: bold;
      }

      .divider {
        background-color: #79a7e3;
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .info-group {
        display: flex;
        align-items: center;
        gap: 5px;
      }

      p {
        margin: 0;
        padding: 0;
        padding-top: 3px;
        color: rgb(49, 71, 103);
      }

      .header-value {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 2px;
        background-color: #fff;
        font-weight: bold;
        color: #000;
        min-width: 30px;
        text-align: center;
      }

      .date-time {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 4px;
        background-color: rgba(255, 255, 153, 255);
        color: #000;
      }

      .location {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 4px;
        background-color: rgba(230, 237, 215, 255);
        font-weight: bold;
        color: #000;
      }

      .table-container {
        width: 100%;
        overflow-x: auto;
        margin-top: 20px;
      }

      .table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
        margin: 0 auto;
        border-spacing: 0;
      }

      .table th {
        padding: 6px 2px;
        text-align: center;
        color: #79a7e3;
        font-weight: bold;
        border-bottom: 1px solid #ddd;
        white-space: nowrap;
        font-size: 12px;
        vertical-align: top;
      }

      .table td {
        padding: 6px 2px;
        text-align: center;
        border-bottom: 1px solid #ddd;
        font-size: 11px;
        word-wrap: break-word;
        overflow: hidden;
      }

      .table-header {
        background-color: #f9f9f9;
      }

      .trip-id-col {
        width: 5%;
      }

      .app-time-col {
        width: 10%;
      }

      .patient-name-col {
        width: 12%;
      }

      .pickup-location-col {
        width: 15%;
      }

      .special-needs-col {
        width: 15%;
      }

      .destination-col {
        width: 15%;
      }

      .status-col {
        width: 8%;
      }

      .performed-col {
        width: 28%;
      }

      .summary-section {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        background-color: #79a7e3;
        padding: 10px;
      }

      .summary-group {
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .undefined-cell {
        font-style: italic;
        color: #777;
      }

      .driver-section {
        page-break-after: always;
      }

      .reason-row td {
        text-align: left;
        background-color: #f9f9f9;
        padding-left: 0;
        border-bottom: 1px solid #ddd;
      }

      .reason-content {
        padding-left: 0;
        display: grid;
        grid-template-columns: 5% 95%;
      }

      .reason-text {
        text-align: left;
        padding-left: 10px;
      }

      .work-header {
        text-align: center;
        font-weight: bold;
        color: #79a7e3;
        width: 28%;
        vertical-align: top;
      }

      th.performed-item {
        font-weight: bold;
        font-size: 12px;
        color: #000000 !important;
        width: 7%;
        text-align: center;
        vertical-align: top;
      }

      .no-bottom-border {
        border-bottom: none !important;
      }

      .main-header-cell {
        vertical-align: top;
        padding: 6px 1px;
      }

      .work-header-cell {
        padding: 6px 0;
      }

      .performed-item-cell {
        padding: 6px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header-section">
        <div class="header">SACS Report Special Transport</div>
        <div class="date-section">
          <p>Date: {{reportQueryDate}}</p>
        </div>
      </div>
      <div class="divider">
        <span class="location">{{medicalCenter}}</span>
        <div class="info-group">
          <p>Total Number Of Trips:</p>
          <span class="header-value">{{totalTrips}}</span>
        </div>
        <span class="date-time">
          {{reportTimeout}}
        </span>
      </div>
      {{#each dataArray}}
        <div class="driver-section">
          <div class="divider">
            <span class="location">{{driverName}}</span>
            <div class="info-group">
              <p>Zone: </p>
              <span class="header-value">{{zone}}</span>
            </div>
            <div class="info-group">
              <p>Vehicle: </p>
              <span class="header-value">{{vehicle}}</span>
            </div>
            <div class="info-group">
              <p>Total Trips:</p>
              <span class="header-value">{{totalTrips}}</span>
            </div>
          </div>
          <div class="table-container">
            <table class="table">
              <thead>
                <tr class="table-header">
                  <th class="trip-id-col main-header-cell" rowspan="2">Trip Id</th>
                  <th class="app-time-col main-header-cell" rowspan="2">App Time</th>
                  <th class="patient-name-col main-header-cell" rowspan="2">Patient Name</th>
                  <th class="pickup-location-col main-header-cell" rowspan="2">Pickup Location</th>
                  <th class="special-needs-col main-header-cell" rowspan="2">Special Needs</th>
                  <th class="destination-col main-header-cell" rowspan="2">Destination</th>
                  <th colspan="4" class="work-header no-bottom-border performed-col work-header-cell">Work Performed</th>
                </tr>
                <tr class="table-header">
                  <th class="performed-item performed-item-cell">On Board</th>
                  <th class="performed-item performed-item-cell">Drop Off</th>
                  <th class="performed-item performed-item-cell">Travel Time</th>
                  <th class="performed-item performed-item-cell">Late Arrival</th>
                </tr>
              </thead>
              <tbody>
                {{#each trips}}
                  <tr>
                    <td>{{tripId}}</td>
                    <td>
                      {{appTime}}
                      <br>
                      <strong>Phones: </strong>{{patientPhones}}
                    </td>
                    <td>{{patientName}}</td>
                    <td>{{pickupLocation}}</td>
                    <td>{{specialNeeds}}</td>
                    <td>{{destination}}</td>
                    <td>{{onBoard}}</td>
                    <td>{{dropOff}}</td>
                    <td>{{travelTime}}</td>
                    <td>{{lateArrival}}</td>
                  </tr>
                  {{#if reason}}
                    {{#ifNotEquals reason "-"}}
                      <tr class="reason-row">
                        <td colspan="10">
                          <div class="reason-content">
                            <div class="reason-empty"></div>
                            <div class="reason-text">
                              <strong>Reason: </strong>{{reason}}
                            </div>
                          </div>
                        </td>
                      </tr>
                    {{/ifNotEquals}}
                  {{/if}}
                {{/each}}
              </tbody>
            </table>
            <div class="divider">
              <div class="info-group">
                <p>Total Trips: </p>
                <span class="header-value">{{totalTrips}}</span>
              </div>
              <div class="info-group">
                <p>Total Completed: </p>
                <span class="header-value">{{completedTrips}}</span>
              </div>
              <div class="info-group">
                <p>Total Cancelled: </p>
                <span class="header-value">{{canceledTrips}}</span>
              </div>
              <div class="info-group">
                <p>Total Late: </p>
                <span class="header-value">{{lateTrips}}</span>
              </div>
              <div class="info-group">
                <p>Effective Work: </p>
                <span class="header-value">{{effectiveWork}} %</span>
              </div>
            </div>
            <br>
          </div>
        </div>
      {{/each}}
    </div>
  </body>
</html>