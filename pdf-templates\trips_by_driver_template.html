<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Sheet By Driver</title>
    <style>
        /* === RESET Y BASE === */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 20px 15px;
            color: #000;
            background-color: #fff;
        }

        /* === LAYOUT PRINCIPAL === */
        .report-container {
            width: 100%;
            padding: 0;
            margin-top: 10px;
        }

        /* === HEADER SECTION === */
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
            padding: 10px 0;
            font-size: 12px;
        }

        .header__company {
            display: flex;
            flex-direction: column;
            margin-left: 5px;
        }

        .header__title {
            font-size: 11px;
            font-weight: bold;
            margin: 5px 0 8px 0;
        }

        .header__driver-info {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 11px;
            margin-top: 8px;
        }

        .driver-info__text {
            font-weight: bold;
        }

        .driver-info__separator {
            width: 30px;
            height: 8px;
            background-color: #999;
            margin: 0 5px;
        }

        .header__report-section {
            text-align: left;
            font-size: 11px;
            font-weight: bold;
            min-width: 300px;
        }

        .report-section__title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
            text-align: center;
        }

        .report-section__date-line {
            margin-bottom: 3px;
            white-space: nowrap;
        }

        .date-line__label {
            display: inline;
        }

        /* === TABLA PRINCIPAL === */
        .trips-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
            margin: 10px 0;
        }

        .trips-table__header {
            background-color: #fff;
            padding: 6px 3px;
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            border-top: 4px solid #999;
            border-bottom: 4px solid #999;
            border-left: none;
            border-right: none;
        }

        .trips-table__cell {
            padding: 3px 4px;
            border-top: none;
            border-bottom: 1px solid #ccc;
            border-left: none;
            border-right: none;
            font-size: 9px;
            vertical-align: top;
            text-align: center;
        }

        /* === COLUMNAS DE LA TABLA === */
        .col--trip-id { width: 6%; }
        .col--app-time { width: 12%; }
        .col--patient { width: 12%; }
        .col--pickup { width: 18%; }
        .col--needs { width: 10%; }
        .col--destination { width: 18%; }
        .col--onboard { width: 6%; }
        .col--dropoff { width: 6%; }
        .col--travel { width: 8%; }
        .col--late { width: 4%; }

        /* === ESTADOS DE FILAS === */
        .trips-table__row--completed {
            background-color: #fff;
        }

        .trips-table__row--not-completed {
            background-color: #fff;
        }

        .trips-table__row--divider .trips-table__cell {
            background-color: #fff;
            border-top: none;
            border-bottom: 1px solid #ccc;
            padding: 8px 8px 8px 90px;
            font-weight: bold;
            font-size: 11px;
            text-align: left;
            position: relative;
        }

        .trips-table__row--divider .trips-table__cell::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            width: 80px;
            height: 2px;
            background-color: #999;
            transform: translateY(-50%);
        }

        .trips-table__row--divider .trips-table__cell::after {
            content: "";
            position: absolute;
            right: 0;
            top: 50%;
            width: calc(100% - 220px);
            height: 2px;
            background-color: #999;
            transform: translateY(-50%);
        }

        /* === ELEMENTOS DE CELDA === */
        .cell__label {
            font-weight: bold;
            font-size: 9px;
        }

        .cell__phone-label {
            font-weight: bold;
            font-size: 9px;
        }

        .cell__reason-label {
            font-weight: bold;
            font-size: 9px;
        }

        /* === FOOTER RESUMEN === */
        .report-summary {
            background-color: #fff;
            border-top: 4px solid #999;
            border-bottom: 4px solid #999;
            padding: 8px 15px;
            margin: 15px 0;
            font-size: 11px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .summary__left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .summary__center {
            display: flex;
            align-items: center;
            gap: 20px;
            flex: 1;
            justify-content: center;
        }

        .summary__right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .summary__driver-name {
            font-weight: bold;
        }

        .summary__separator {
            width: 100px;
            height: 8px;
            background-color: #999;
            margin: 0 10px;
        }

        .summary__item {
            display: flex;
            align-items: center;
            gap: 5px;
            white-space: nowrap;
        }

        .summary__value {
            font-weight: bold;
        }

        .summary__value--highlighted {
            background-color: #d3d3d3;
            padding: 2px 8px;
            border-radius: 3px;
        }

        /* === UTILIDADES === */
        .u-page-number {
            font-size: 10px;
            text-align: center;
            margin-top: 20px;
        }

        /* === RESPONSIVE === */
        @media print {
            body {
                padding: 0;
            }

            .report-container {
                width: 100%;
            }
        }

        /* Tablets y pantallas medianas */
        @media (max-width: 1024px) {
            .report-header {
                gap: 15px;
            }

            .header__report-section {
                min-width: 250px;
            }

            .report-summary {
                padding: 6px 10px;
                gap: 15px;
            }

            .summary__separator {
                width: 60px;
            }
        }

        /* Tablets pequeñas */
        @media (max-width: 768px) {
            body {
                padding: 5px;
                font-size: 10px;
            }

            .report-header {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .header__company,
            .header__report-section {
                text-align: center;
                min-width: auto;
            }

            .header__driver-info {
                justify-content: center;
                flex-wrap: wrap;
            }

            .driver-info__separator {
                width: 20px;
                height: 6px;
            }

            .trips-table {
                font-size: 8px;
            }

            .trips-table__header,
            .trips-table__cell {
                padding: 2px 1px;
            }

            .cell__phone-label,
            .cell__reason-label {
                font-size: 8px;
            }

            .report-summary {
                flex-direction: column;
                gap: 10px;
                padding: 10px;
            }

            .summary__left,
            .summary__center,
            .summary__right {
                justify-content: center;
                flex-wrap: wrap;
                gap: 10px;
            }

            .summary__separator {
                width: 40px;
                height: 6px;
            }
        }

        /* Móviles */
        @media (max-width: 480px) {
            body {
                padding: 2px;
                font-size: 9px;
            }

            .report-header {
                gap: 8px;
            }

            .header__driver-info {
                font-size: 9px;
                gap: 2px;
            }

            .driver-info__separator {
                width: 15px;
                height: 4px;
                margin: 0 3px;
            }

            .report-section__title {
                font-size: 12px;
            }

            .trips-table {
                font-size: 7px;
            }

            .trips-table__header {
                padding: 2px 1px;
                font-size: 8px;
            }

            .trips-table__cell {
                padding: 1px;
                font-size: 7px;
                line-height: 1.2;
            }

            /* Ajustar anchos de columnas para móviles */
            .col--trip-id { width: 8%; }
            .col--app-time { width: 15%; }
            .col--patient { width: 12%; }
            .col--pickup { width: 20%; }
            .col--needs { width: 8%; }
            .col--destination { width: 20%; }
            .col--onboard { width: 7%; }
            .col--dropoff { width: 5%; }
            .col--travel { width: 8%; }
            .col--late { width: 5%; }

            .cell__phone-label,
            .cell__reason-label {
                font-size: 7px;
            }

            .report-summary {
                font-size: 9px;
                padding: 8px;
                gap: 8px;
            }

            .summary__left,
            .summary__center,
            .summary__right {
                gap: 8px;
            }

            .summary__separator {
                width: 30px;
                height: 5px;
                margin: 0 5px;
            }

            .summary__item {
                gap: 3px;
            }
        }

        /* Pantallas muy grandes */
        @media (min-width: 1200px) {
            .report-container {
                max-width: 1200px;
                margin: 0 auto;
            }
        }
    </style>
</head>

<body>
    <div class="report-container">
        <!-- Header principal -->
        <header class="report-header">
            <div class="header__company">
                <div class="header__title">Driver: {{driverName}}</div>
                <div class="header__driver-info">
                    <span class="driver-info__text">Zone: {{zone}}</span>
                    <div class="driver-info__separator"></div>
                    <span class="driver-info__text">Vehicle: {{vehicle}}</span>
                    <span class="driver-info__text">Total Trips: {{totalTrips}}</span>
                </div>
            </div>

            <div class="header__report-section">
                <div class="report-section__title">SACS | Trips By Driver Report</div>
            </div>

            <div class="header__report-section">
                <div class="report-section__date-line">
                    <span class="date-line__label">Date:</span> {{reportQueryDate}}
                </div>
                <div class="report-section__date-line">
                    <span class="date-line__label">Report Time Out:</span> {{reportTimeout}}
                </div>
            </div>
        </header>

        <!-- Tabla principal -->
        <main>
            {{#each dataArray}}
            <div style="margin-top: 10px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px; margin-left: 5px;">
                    <div>
                        <p><strong>Medical Center: </strong>{{this.medicalCenterName}}</p>
                    </div>
                </div>

                <table class="trips-table">
                    <thead>
                        <tr>
                            <th class="trips-table__header col--trip-id">Trip ID</th>
                            <th class="trips-table__header col--app-time">App Time/Phones</th>
                            <th class="trips-table__header col--patient">Patient Name</th>
                            <th class="trips-table__header col--pickup">Pickup Loc/Reason</th>
                            <th class="trips-table__header col--needs">Special Needs</th>
                            <th class="trips-table__header col--destination">Destination</th>
                            <th class="trips-table__header col--onboard">On Board</th>
                            <th class="trips-table__header col--dropoff">Drop Off</th>
                            <th class="trips-table__header col--travel">Travel Time</th>
                            <th class="trips-table__header col--late">Late</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each this.completedTrips}}
                        <tr class="trips-table__row--completed">
                            <td class="trips-table__cell col--trip-id">{{this.tripId}}</td>
                            <td class="trips-table__cell col--app-time">
                                {{this.appTime}}<br>
                                <span class="cell__phone-label">Phones:</span> {{this.patientPhones}}
                            </td>
                            <td class="trips-table__cell col--patient">{{this.patientName}}</td>
                            <td class="trips-table__cell col--pickup">
                                {{this.pickupLocation}}<br>
                                <span class="cell__reason-label">Reason:</span> {{this.reason}}
                            </td>
                            <td class="trips-table__cell col--needs">{{this.specialNeeds}}</td>
                            <td class="trips-table__cell col--destination">{{this.destination}}</td>
                            <td class="trips-table__cell col--onboard">{{this.onBoard}}</td>
                            <td class="trips-table__cell col--dropoff">{{this.dropOff}}</td>
                            <td class="trips-table__cell col--travel">{{this.travelTime}}</td>
                            <td class="trips-table__cell col--late">{{this.lateArrival}}</td>
                        </tr>
                        {{/each}}

                        <tr class="trips-table__row--divider">
                            <td class="trips-table__cell" colspan="10">Not Completed Trips</td>
                        </tr>

                        {{#each this.notCompletedTrips}}
                        <tr class="trips-table__row--not-completed">
                            <td class="trips-table__cell col--trip-id">{{this.tripId}}</td>
                            <td class="trips-table__cell col--app-time">
                                {{this.appTime}}<br>
                                <span class="cell__phone-label">Phones:</span> {{this.patientPhones}}
                            </td>
                            <td class="trips-table__cell col--patient">{{this.patientName}}</td>
                            <td class="trips-table__cell col--pickup">
                                {{this.pickupLocation}}<br>
                                <span class="cell__reason-label">Reason:</span> {{this.reason}}
                            </td>
                            <td class="trips-table__cell col--needs">{{this.specialNeeds}}</td>
                            <td class="trips-table__cell col--destination">{{this.destination}}</td>
                            <td class="trips-table__cell col--onboard">{{this.onBoard}}</td>
                            <td class="trips-table__cell col--dropoff">{{this.dropOff}}</td>
                            <td class="trips-table__cell col--travel">{{this.travelTime}}</td>
                            <td class="trips-table__cell col--late">{{this.lateArrival}}</td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>

                <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                    <div>
                        <p><strong>Total Completed: </strong>{{this.completedTripsCount}}</p>
                    </div>
                    <div>
                        <p><strong>Total Late: </strong>{{this.totalLateTrips}}</p>
                    </div>
                </div>
            </div>
            {{/each}}
        </main>

        <footer class="report-summary">
            <div class="summary__left">
                <span class="summary__driver-name">{{driverName}}</span>
                <div class="summary__separator"></div>
            </div>

            <div class="summary__center">
                <div class="summary__item">
                    Vehicle: <span class="summary__value">{{vehicle}}</span>
                </div>

                <div class="summary__item">
                    Total Trips: <span class="summary__value">{{totalTrips}}</span>
                </div>

                <div class="summary__item">
                    Total Completed: <span class="summary__value">{{completedTrips}}</span>
                </div>

                <div class="summary__item">
                    Total Not Completed: <span class="summary__value">{{notCompletedTrips}}</span>
                </div>
            </div>

            <div class="summary__right">
                <div class="summary__separator"></div>

                <div class="summary__item">
                    Effective Work: <span class="summary__value">{{effectiveWork}}%</span>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
