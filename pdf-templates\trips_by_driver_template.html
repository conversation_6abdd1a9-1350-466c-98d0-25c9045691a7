<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Sheet By Driver</title>
    <style>
        /* === RESET Y BASE === */
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 10px;
            color: #000;
            background-color: #fff;
        }
        
        /* === LAYOUT PRINCIPAL === */
        .container {
            width: 100%;
            padding: 0;
        }
        
        /* === HEADER SECTION === */
        .header {
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
            text-align: center;
        }
        
        .divider {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #79a7e3;
            padding: 10px 0;
        }
        
        .info-group {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 11px;
        }
        
        p {
            margin: 0;
            padding: 0;
            padding-top: 3px;
            color: rgb(49, 71, 103);
        }
        
        .header-value {
            padding: 5px 10px;
            border: 1px solid #000;
            border-radius: 2px;
            background-color: #fff;
            font-weight: bold;
            color: #000;
            min-width: 30px;
            text-align: center;
        }
        
        .date-time {
            padding: 5px 10px;
            margin-right: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(255, 255, 153, 255);
            color: #000;
        }
        
        .location {
            padding: 5px 10px;
            margin-left: 5px;
            border: 1px solid #000;
            border-radius: 4px;
            background-color: rgba(230, 237, 215, 255);
            font-weight: bold;
            color: #000;
        }
        
        /* === TABLA PRINCIPAL === */
        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
            margin: 10px 0;
            table-layout: fixed;
        }
        
        .table-header th {
            background-color: #fff;
            padding: 6px 3px;
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            border-top: 4px solid #999;
            border-bottom: 4px solid #999;
            border-left: none;
            border-right: none;
            color: #79a7e3;
        }
        
        .table-data td {
            padding: 3px 4px;
            border-top: none;
            border-bottom: 1px solid #ccc;
            border-left: none;
            border-right: none;
            font-size: 9px;
            vertical-align: top;
            text-align: center;
        }
        
        /* === COLUMNAS DE LA TABLA === */
        .table-header th:nth-child(1) { width: 6%; }
        .table-header th:nth-child(2) { width: 12%; }
        .table-header th:nth-child(3) { width: 12%; }
        .table-header th:nth-child(4) { width: 18%; }
        .table-header th:nth-child(5) { width: 10%; }
        .table-header th:nth-child(6) { width: 18%; }
        
        .work-header {
            text-align: center;
            font-weight: bold;
            color: #79a7e3;
            width: 24%;
        }
        
        th.performed-item {
            font-weight: bold;
            font-size: 10px;
            color: #000000 !important;
            width: 6%;
        }
        
        .total-trips {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-top: 10px;
        }
        
        .query-date {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 10px;
        }
        
        /* === RESPONSIVE === */
        @media print {
            body {
                padding: 0;
            }
            
            .container {
                width: 100%;
            }
        }
        
        @media (max-width: 1024px) {
            .divider {
                gap: 15px;
            }
            
            .info-group {
                min-width: auto;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 5px;
                font-size: 10px;
            }
            
            .divider {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }
            
            .table {
                font-size: 8px;
            }
            
            .table-header th,
            .table-data td {
                padding: 2px 1px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 2px;
                font-size: 9px;
            }
            
            .divider {
                gap: 8px;
            }
            
            .table {
                font-size: 7px;
            }
            
            .table-header th {
                padding: 2px 1px;
                font-size: 8px;
            }
            
            .table-data td {
                padding: 1px;
                font-size: 7px;
                line-height: 1.2;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">SACS Report By Driver</div>
        <div class="divider">
            <span class="location">{{driverName}}</span>
            <div class="info-group">
              <p>Zone: </p><span class="header-value">{{zone}}</span>
            </div>
            <div class="info-group">
              <p>Vehicle: </p><span class="header-value">{{vehicle}}</span>
            </div>
             <div class="info-group">
              <p>Total Number Of Trips:</p><span class="header-value">{{totalTrips}}</span>
            </div>
            <span class="date-time">
                <p>{{reportTimeout}}</p>
            </span>
        </div>
        <br>
         <div style="display: flex; justify-content: space-between;">
            <div class="total-trips">
                <span></span>
            </div>
            <div class="query-date">
                <p><strong>Date:</strong>{{reportQueryDate}}</p>
            </div>
        </div>
        <br>

        {{#each dataArray}}
        <div style="margin-top: 30px;">
          <div style="display: flex; justify-content: space-between;">
            <div class="total-trips">
              <p><strong>Medical Center: </strong>{{this.medicalCenterName}}</p>
            </div>
          </div>
          <div class="divider">
            <span class="location">Completed Trips</span>
          </div>
          <br>
          <table class="table">
            <tr class="table-header">
              <th rowspan="2">Trip Id</th>
              <th rowspan="2">App Time</th>
              <th rowspan="2">Patient Name</th>
              <th rowspan="2">Pickup Location</th>
              <th rowspan="2">Special Needs</th>
              <th rowspan="2">Destination</th>
              <th colspan="4" class="work-header">Work Performed</th>
            </tr>
            <tr class="table-header">
              <th class="performed-item">On Board</th>
              <th class="performed-item">Drop Off</th>
              <th class="performed-item">Travel Time</th>
              <th class="performed-item">Late Arrival</th>
            </tr>

            {{#each this.completedTrips}}
            <tr class="table-data">
              <td>{{this.tripId}}</td>
              <td>{{this.appTime}}<br><strong>Phones: </strong>{{this.patientPhones}}</td>
              <td>{{this.patientName}}</td>
              <td>{{this.pickupLocation}}<br><strong>Reason: </strong>{{this.reason}}</td>
              <td>{{this.specialNeeds}}</td>
              <td>{{this.destination}}</td>
              <td>{{this.onBoard}}</td>
              <td>{{this.dropOff}}</td>
              <td>{{this.travelTime}}</td>
              <td>{{this.lateArrival}}</td>
            </tr>
            {{/each}}

          </table>
          <div style="display: flex; justify-content: space-between;">
            <div class="total-trips">
                <p><strong>Total Completed: </strong>{{this.completedTripsCount}}</p>
            </div>
            <div class="query-date">
                <p><strong>Total Late: </strong>{{this.totalLateTrips}}</p>
            </div>
          </div>
        </div>

        <div style="margin-top: 30px;">
          <div class="divider">
            <span class="location">Not Completed Trips</span>
          </div>
          <br>
          <table class="table">
            <tr class="table-header">
              <th rowspan="2">Trip Id</th>
              <th rowspan="2">App Time</th>
              <th rowspan="2">Patient Name</th>
              <th rowspan="2">Pickup Location</th>
              <th rowspan="2">Special Needs</th>
              <th rowspan="2">Destination</th>
              <th colspan="4" class="work-header">Work Performed</th>
            </tr>
            <tr class="table-header">
              <th class="performed-item">On Board</th>
              <th class="performed-item">Drop Off</th>
              <th class="performed-item">Travel Time</th>
              <th class="performed-item">Late Arrival</th>
            </tr>

            {{#each this.notCompletedTrips}}
            <tr class="table-data">
              <td>{{this.tripId}}</td>
              <td>{{this.appTime}}<br><strong>Phones: </strong>{{this.patientPhones}}</td>
              <td>{{this.patientName}}</td>
              <td>{{this.pickupLocation}}<br><strong>Reason: </strong>{{this.reason}}</td>
              <td>{{this.specialNeeds}}</td>
              <td>{{this.destination}}</td>
              <td>{{this.onBoard}}</td>
              <td>{{this.dropOff}}</td>
              <td>{{this.travelTime}}</td>
              <td>{{this.lateArrival}}</td>
            </tr>
            {{/each}}

          </table>
        </div>
        <br>
        {{/each}}

        <div class="divider">
          <span class="location">{{driverName}}</span>
          <div class="info-group">
            <p>Vehicle: </p><span class="header-value">{{vehicle}}</span>
          </div>
          <div class="info-group">
            <p>Total Trips: </p><span class="header-value">{{totalTrips}}</span>
          </div>
          <div class="info-group">
            <p>Total Completed: </p><span class="header-value">{{completedTrips}}</span>
          </div>
          <div class="info-group">
            <p>Total Not Completed: </p><span class="header-value">{{notCompletedTrips}}</span>
          </div>
          <div class="info-group">
            <p>Effective Work: </p><span class="header-value">{{effectiveWork}} %</span>
          </div>
        </div>
    </div>
</body>
</html>
