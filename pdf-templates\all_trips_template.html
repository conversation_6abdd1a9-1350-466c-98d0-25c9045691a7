<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>All Trips Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }
    .container {
      width: 100%;
      padding: 20px;
      text-align: center;
      box-sizing: border-box;
    }
    .header-section {
      position: relative;
      margin-bottom: 20px;
    }
    .header {
      font-size: 18px;
      font-weight: bold;
      margin: 10px 0;
      text-align: center;
    }
    .date-section {
      position: absolute;
      top: 0;
      right: 0;
      text-align: right;
    }
    .date-section p {
      margin: 0;
      font-weight: bold;
    }
     .divider {
        background-color: #79a7e3;
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
    .location {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 4px;
        background-color: rgba(230, 237, 215, 255);
        font-weight: bold;
        color: #000;
      }
   .date-time {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 4px;
        background-color: rgba(255, 255, 153, 255);
        color: #000;
      }
    .info-group {
      display: flex;
      align-items: center;
      margin: 0 10px;
    }

    p {
        margin: 0;
        padding: 0;
        padding-top: 3px;
        color: rgb(49, 71, 103);
      }

    .header-value {
        padding: 5px 10px;
        border: 1px solid #000;
        border-radius: 2px;
        background-color: #fff;
        font-weight: bold;
        color: #000;
        min-width: 30px;
        text-align: center;
    }

    .table-container {
        width: 100%;
        overflow-x: auto;
        margin-top: 20px;
      }

    .table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
        margin: 0 auto;
        border-spacing: 0;
      }

    .table th {
        padding: 6px 2px;
        text-align: center;
        color: #79a7e3;
        font-weight: bold;
        border-bottom: 1px solid #ddd;
        white-space: nowrap;
        font-size: 12px;
        vertical-align: top;
      }

      .table td {
        padding: 6px 2px;
        text-align: center;
        border-bottom: 1px solid #ddd;
        font-size: 11px;
        word-wrap: break-word;
        overflow: hidden;
      }

      .table-header {
        background-color: #f9f9f9;
      }

    .center-section {
      margin-bottom: 30px;
      page-break-after: always;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header-section">
      <div class="header">SACS Report All Trips</div>
        <div class="date-section">
          <p>Date: {{reportQueryDate}}</p>
        </div>
    </div>

    <div class="divider">
      <span class="location">All Medical Centers</span>
      <div class="info-group">
        <p>Total Trips:</p><span class="header-value">{{totalTrips}}</span>
      </div>
      <div class="info-group">
        <p>Completed:</p><span class="header-value">{{totalCompleted}}</span>
      </div>
      <div class="info-group">
        <p>Cancelled:</p><span class="header-value">{{totalCancelled}}</span>
      </div>
      <div class="info-group">
        <p>Pending:</p><span class="header-value">{{pendingTrips}}</span>
      </div>
      <span class="date-time">
        {{reportTimeout}}
      </span>
    </div>

    {{#each groupedTrips}}
    <div class="center-section">
      <div class="divider">
        <span class="location">{{@key}}</span>
        <div class="info-group">
          <p>Total Trips:</p><span class="header-value">{{this.totalTrips}}</span>
        </div>
        <div class="info-group">
          <p>Completed:</p><span class="header-value">{{this.completedTrips}} ({{this.completedPercentage}}%)</span>
        </div>
        <div class="info-group">
          <p>Cancelled:</p><span class="header-value">{{this.cancelledTrips}} ({{this.cancelledPercentage}}%)</span>
        </div>
        <div class="info-group">
          <p>Pending:</p><span class="header-value">{{this.pendingTrips}} ({{this.pendingPercentage}}%)</span>
        </div>
      </div>

      <div class="table-container">
        <table class="table">
          <thead>
            <tr class="table-header">
              <th>Driver Name</th>
              <th>Scheduled</th>
              <th>Completed</th>
              <th>Completed (%)</th>
              <th>Cancelled</th>
              <th>Cancelled (%)</th>
              <th>Pending</th>
              <th>Pending (%)</th>
            </tr>
          </thead>
          <tbody>
            {{#each this.drivers}}
            <tr>
              <td>{{@key}}</td>
              <td>{{this.stats.Scheduled}}</td>
              <td>{{this.stats.Completed}}</td>
              <td>{{this.stats.CompletedPercentage}}%</td>
              <td>{{this.stats.Cancelled}}</td>
              <td>{{this.stats.CancelledPercentage}}%</td>
              <td>{{this.stats.Pending}}</td>
              <td>{{this.stats.PendingPercentage}}%</td>
            </tr>
            {{/each}}
          </tbody>
        </table>
        <br>
      </div>
    </div>
    {{/each}}
  </div>
</body>
</html>
